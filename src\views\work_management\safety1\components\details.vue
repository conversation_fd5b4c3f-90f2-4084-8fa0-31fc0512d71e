<template>
  <div class="app-container">
    <div class="top_view">
      <div
        v-for="(item, index) in querybox"
        :key="index"
        @click="handletab(index, item.title)"
        class="top_view_box"
      >
        <div>
          <p>{{ item.title }}</p>
          <p>{{ item.num }}</p>
        </div>
        <div class="icon-container">
          <component
            :is="item.icon"
            class="status-icon"
            :style="getIconStyle(index)"
          />
        </div>
      </div>
    </div>

    <!-- 搜索栏-旧 -->
    <!-- <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="工单提交时间" prop="createTime">
          <el-date-picker
            class="!w-[240px]"
            v-model="queryParams.createTime"
            type="daterange"
            range-separator="~"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="申请人部门" prop="deptId">
          <el-tree-select
            v-model="queryParams.deptId"
            placeholder="请选择申请部门"
            :data="deptOptions"
            clearable
            filterable
            check-strictly
            :render-after-expand="false"
            @keyup.enter="handleQuery()"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleQuery()">
            <i-ep-search />
            搜索
          </el-button>
          <el-button @click="handleResetQuery()">
            <i-ep-refresh />
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div> -->

    <el-card class="mb-6 search-card">
      <!-- 搜索栏 -->
      <div class="flex flex-col">
        <div class="flex flex-1 gap-2 ">
          <!-- 搜索输入框 -->
          <el-input
            v-model="queryParams.inventoryName"
            placeholder="搜索任务名称、发起部门、发起人员..."
            class="flex-1"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>

          <!-- 搜索按钮 -->
          <el-button
            type="primary"
            class="flex items-center gap-2"
            @click="handleQuery"
          >
            <el-icon><Search /></el-icon>
            <span>搜索</span>
          </el-button>

          <!-- 重置按钮 -->
          <el-button class="flex items-center gap-2" @click="handleResetQuery">
            <el-icon><Refresh /></el-icon>
            <span>重置</span>
          </el-button>

          <!-- 筛选按钮 -->
          <el-button
            class="flex items-center gap-2 ml-auto"
            @click="toggleAdvancedFilters"
          >
            <el-icon><Filter /></el-icon>
            <span>筛选</span>
            <el-icon
              class="transition-transform duration-300"
              :class="{ 'rotate-180': showAdvancedFilters }"
            >
              <ArrowDown />
            </el-icon>
          </el-button>
        </div>
      </div>

      <!-- 筛选面板 -->
      <div v-if="showAdvancedFilters" class="advanced-filters">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="工单提交时间" prop="createTime" class="filters-form-item">
            <el-date-picker
              class="!w-[240px]"
              v-model="queryParams.createTime"
              type="daterange"
              range-separator="~"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>

          <el-form-item label="申请人部门" prop="deptId" class="filters-form-item">
            <el-tree-select
              v-model="queryParams.deptId"
              placeholder="请选择申请部门"
              :data="deptOptions"
              clearable
              filterable
              check-strictly
              :render-after-expand="false"
              @keyup.enter="handleQuery()"
            />
          </el-form-item>

          <!-- 应用筛选按钮 -->
          <el-form-item class="apply-filters filters-form-item">
            <el-button
              type="primary"
              @click="handleQuery()"
            >
              应用筛选
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <el-card shadow="never" class="table-container">
      <template #header>
        <div class="flex-x-between">
          <div>
            <el-button
              v-hasPerm="['system:safety:add']"
              type="success"
              @click="handleOpenDialog(undefined)"
            >
              <i-ep-plus />
              发起工单
            </el-button>
            <el-button
              v-hasPerm="['system:safety:delete']"
              type="danger"
              :disabled="ids.length === 0"
              @click="handleDelete()"
            >
              <i-ep-delete />
              删除
            </el-button>
          </div>
          <div>
            <el-button class="ml-3" @click="handleExport">
              <template #icon><i-ep-upload /></template>
              导入
            </el-button>

            <el-button class="ml-3" @click="handleExport">
              <template #icon><i-ep-download /></template>
              导出
            </el-button>
          </div>
        </div>
      </template>
      <el-table
        ref="dataTableRef"
        :default-sort="{ prop: 'id', order: 'descending' }"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />

        <!-- 状态、截止日期、漏洞来源、信息系统名称（原“任务名称”）、域名/IP（新增）、安全漏洞描述（原“主要内容”）、漏洞报告、责任单位、责任人、联系方式、任务创建时间（原工单提交时间） -->
        <!-- 1:'待处理',11:'逾期待处理',
              2:'待审核',12:'逾期待审核',
              3:'流转中',13:'逾期流转中',
              4:'已完成', 14:'逾期完成',
              -->
        <el-table-column
          key="status"
          label="状态"
          prop="status"
          min-width="100"
          align="center"
        >
          <template #default="scope">
            <el-tag
              :type="
                scope.row.safetyStatus == 0 ||
                scope.row.safetyStatus == 11 ||
                scope.row.safetyStatus == 1 ||
                scope.row.safetyStatus == 2 ||
                scope.row.safetyStatus == 12
                  ? 'warning'
                  : scope.row.safetyStatus == 4 || scope.row.safetyStatus == 14
                    ? 'success'
                    : 'info'
              "
            >
              {{ statusbox[scope.row.safetyStatus] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          key="deadline"
          label="截止日期"
          prop="deadline"
          min-width="120"
          align="center"
        >
          <template #default="scope">
            {{ formatDateOnly(scope.row.deadline) }}
          </template>
        </el-table-column>
        <el-table-column
          key="loopholeSource"
          label="漏洞来源"
          prop="loopholeSource"
          min-width="100"
          align="center"
        >
          <template #default="scope">
            <Dictmap v-model="scope.row.loopholeSource" code="VLUNS_O" />
          </template>
        </el-table-column>
        <el-table-column
          key="title"
          label="信息系统名称"
          prop="title"
          min-width="160"
          align="center"
        />
        <el-table-column
          key="domainIp"
          label="域名/IP"
          prop="domainIp"
          min-width="100"
          align="center"
        />
        <el-table-column
          key="content"
          label="安全漏洞描述"
          prop="content"
          min-width="150"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          key="newFileUrl"
          label="漏洞报告"
          prop="fileList"
          min-width="100"
          align="center"
        >
          <template #default="scope">
            <el-button
              v-if="scope.row.newFileUrl"
              size="small"
              @click="
                downloadFile2(scope.row.newFileUrl, scope.row.newFileName)
              "
            >
              下载文件
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          key="businessDeptName"
          label="责任单位"
          prop="businessDeptName"
          min-width="200"
          align="center"
        />
        <el-table-column
          key="businessUserName"
          label="责任人"
          prop="businessUserName"
          min-width="100"
          align="center"
        />
        <el-table-column
          key="mobile"
          label="联系方式"
          prop="mobile"
          min-width="200"
          align="center"
        />
        <el-table-column
          key="createTime"
          label="任务创建时间"
          prop="createTime"
          min-width="150"
          align="center"
        />
        <el-table-column fixed="right" label="操作" width="250">
          <template #default="scope">
            <el-button
              type="info"
              v-hasPerm="[
                'system:safety:audit',
                'system:safety:fix',
                'system:safety:submitVulns',
                'system:safety:commentsVulns',
              ]"
              size="small"
              link
              @click="handleViewTicket(scope.row.id)"
            >
              <i-ep-view />
              查看工单
            </el-button>
            <el-button
              v-if="
                scope.row.step != 6 &&
                scope.row.reviewDeptType.includes(userInfo.deptType)
              "
              type="primary"
              size="small"
              link
              @click="handleOpenDialog(scope.row.id)"
            >
              <i-ep-edit />
              处理工单
            </el-button>
            <el-button
              v-if="scope.row.step != 6"
              v-hasPerm="['system:safety:delete']"
              type="danger"
              size="small"
              link
              @click="handleDelete(scope.row.id)"
            >
              <i-ep-delete />
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 文件列表对话框 -->
    <el-dialog v-model="fileListDialog.visible" title="文件列表" width="50%">
      <el-table :data="fileListDialog.files" style="width: 100%">
        <el-table-column prop="linkName" label="环节名称" />
        <el-table-column prop="name" label="文件名" />
        <el-table-column prop="createByName" label="创建人名称" />
        <el-table-column prop="createTime" label="上传时间" />
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="downloadFile(scope.row)"
            >
              下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>

  <ViewTicket
    v-model:visible="viewTicketDialog.visible"
    :ticket-id="viewTicketDialog.ticketId"
    @navigateToProcess="handleOpenDialog"
  />
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";

defineOptions({
  name: "safety",
  inheritAttrs: false,
});

import safetyAPI, {
  safetyPageVO,
  safetyForm,
  safetyPageQuery,
} from "@/api/work_management/safety";
import { useDictStore } from "@/store/modules/dictStore";
import ViewTicket from "./ViewTicket.vue";
import UserAPI, { UserInfo, UserQuery } from "@/api/user";
import DeptAPI from "@/api/dept";
import { formatDateOnly } from "@/utils/dateUtils";
import { useUserStore } from "@/store/modules/user";
import {
  Clock,
  DocumentChecked,
  CircleClose,
  CircleCheckFilled,
  Search
} from "@element-plus/icons-vue";
const props = defineProps({
  ticketId: Number,
  id: Number,
});
const emits = defineEmits(["navigateToProcess"]);

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);
const deptOptions = ref<any[]>([]);
const userList = ref<any[]>([]);
const userInfo = ref<UserInfo>({ perms: [], roles: [] });

const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<safetyPageQuery>({
  pageNum: 1,
  pageSize: 10,
  status: "",
});

const userParams = reactive<UserQuery>({});
// 列表订单状态
const statusbox = ref({
  1: "待处理",
  11: "逾期待处理",
  2: "待审核",
  12: "逾期待审核",
  3: "流转中",
  13: "逾期流转中",
  4: "已完成",
  14: "逾期完成",
});
const querybox = ref([
  { title: "待处理", num: 0, icon: Clock, active: false, status: 1 },
  { title: "待审核", num: 0, icon: DocumentChecked, active: false, status: 2 },
  { title: "未完成", num: 0, icon: CircleClose, active: false, status: 4 },
  {
    title: "已完成",
    num: 0,
    icon: CircleCheckFilled,
    active: false,
    status: 3,
  },
]);
//  搜索查询状态
const statusquery: any = ref(null);
// 获取图标样式
const getIconStyle = (index: number) => {
  const colors = ["#f56c6c", "#e6a23c", "#909399", "#67c23a"]; // 红色、橙色、灰色、绿色
  return {
    color: colors[index] || "#909399",
    fontSize: "32px",
  };
};

// 点击tab
const handletab = (index: number, title: string) => {
  querybox.value[index].active = !querybox.value[index].active;
  querybox.value.forEach((item, indexc) => {
    if (title != item.title) {
      item.active = false;
    }
    if (querybox.value[index].active == true)
      statusquery.value = querybox.value[index].status;
    else statusquery.value = null;
  });
  queryParams.pageNum = 1;
  handleQuery();
};
// 安全问题管理表格数据
const pageData = ref<safetyPageVO[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

// 安全问题管理表单数据
const formData = reactive<safetyForm>({});

// 获取字典存储
const dictStore = useDictStore();

// 需要预加载的字典类型，使用ref以便动态更新
const requiredDictTypes = ref([
  "VLUNS_TIC", // 状态字典
  "VLUNS_STEP", // 步骤字典
  "system0x0", // 系统名称字典
  "dept0x0", // 部门字典
  "VLUNS_O", // 漏洞来源字典
]);

// 记录字典是否已加载
const dictLoaded = ref(false);

// 状态筛选处理
const handleStatusFilter = (status: string) => {
  queryParams.status = status;
  queryParams.pageNum = 1; // 重置到第一页
  handleQuery();
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    "": "全部工单",
    "1": "待处理工单",
    "2": "待审核工单",
    "3": "已完成工单",
  };
  return statusMap[status] || "未知状态";
};

/**
 * 预加载字典数据
 * 在表格数据加载前，预先加载所有可能用到的字典数据
 */
async function preloadDictData() {
  // 如果已加载，则跳过
  if (dictLoaded.value || requiredDictTypes.value.length === 0) {
    return;
  }

  try {
    console.log("正在预加载字典数据...");
    const dictPromises = requiredDictTypes.value.map((type) =>
      dictStore.fetchOptions(type)
    );
    await Promise.all(dictPromises);
    console.log("字典数据预加载完成");

    // 标记字典已加载
    dictLoaded.value = true;
  } catch (error) {
    console.error("字典数据预加载失败:", error);
  }
}

// 工单详情查看对话框
const viewTicketDialog = reactive({
  visible: false,
  ticketId: undefined as number | undefined,
});

// 打开工单详情查看对话框
function handleViewTicket(id: number) {
  console.log("查看工单详情:", id);
  if (!id) {
    ElMessage.warning("工单ID不存在");
    return;
  }

  // 设置对话框数据
  viewTicketDialog.ticketId = id;
  viewTicketDialog.visible = true;
}

/** 查询安全问题管理 */
async function handleQuery() {
  loading.value = true;

  try {
    // 首先预加载字典数据（只在第一次加载时执行）
    if (!dictLoaded.value) {
      await preloadDictData();
    }

    // 获取表格数据
    const data = await safetyAPI.getPage({
      ...queryParams,
      status: statusquery.value,
    });
    pageData.value = data.list;
    console.log("pageData", pageData.value);
    total.value = data.total;
  } catch (error) {
    console.error("查询数据失败:", error);
    ElMessage.error("获取数据失败，请稍后重试");
  } finally {
    loading.value = false;
  }
}

/** 重置安全问题管理查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  queryParams.status = ""; // 重置状态筛选
  handleQuery();
}

// 获取部门选项
const loadDeptOptions = async () => {
  try {
    const data = await DeptAPI.getOptions();
    deptOptions.value = data;
  } catch (error) {
    console.error("加载部门列表失败:", error);
    ElMessage.error("加载部门列表失败");
  }
};

// 获取用户选项
const loadUserList = async () => {
  try {
    const data = await UserAPI.getList(userParams);
    userList.value = data;
  } catch (error) {}
};

// 获取用户选项
const loadUserInfo = async () => {
  try {
    const userStore = useUserStore();
    const data = await userStore.getUserInfo();
    console.log("userInfo", data);
    userInfo.value = data;
    console.log("userInfo", userInfo.value);
  } catch (error) {}
};

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

/** 打开安全问题流程 */
function handleOpenDialog(id?: number) {
  const params = id;
  emits("navigateToProcess", params);
}

/** 删除安全问题管理 */
function handleDelete(id?: number) {
  const removeIds = [id || ids.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      safetyAPI
        .deleteByIds(removeIds)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

/** 导出工单 */
function handleExport() {
  safetyAPI.export(queryParams).then((response: any) => {
    const fileData = response.data;
    const fileName = decodeURI(
      response.headers["content-disposition"].split(";")[1].split("=")[1]
    );
    const fileType =
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8";

    const blob = new Blob([fileData], { type: fileType });
    const downloadUrl = window.URL.createObjectURL(blob);

    const downloadLink = document.createElement("a");
    downloadLink.href = downloadUrl;
    downloadLink.download = fileName;

    document.body.appendChild(downloadLink);
    downloadLink.click();

    document.body.removeChild(downloadLink);
    window.URL.revokeObjectURL(downloadUrl);
  });
}

// 文件列表对话框
const fileListDialog = reactive<any>({
  visible: false,
  files: [] as { name: string; url: string }[],
});

// 显示文件列表
function showFileList(id: number) {
  safetyAPI.getFileList(id).then((fileList) => {
    console.log("fileList", fileList);
    fileListDialog.files = fileList;
    fileListDialog.visible = true;
  });
}

// 下载文件
function downloadFile(file: { name: string; url: string }) {
  if (file.url) {
    fetch(file.url)
      .then((response) => response.blob())
      .then((blob) => {
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = file.name; // 设置自定义文件名
        link.style.display = "none"; // 隐藏链接
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      })
      .catch((error) => {
        ElMessage.error("附件不存在");
      });
  }
}
function downloadFile2(file, newFileName) {
  if (file) {
    fetch(file)
      .then((response) => response.blob())
      .then((blob) => {
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = newFileName; // 设置自定义文件名
        link.style.display = "none"; // 隐藏链接
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      })
      .catch((error) => {
        ElMessage.error("附件不存在");
      });
  }
}
// 获取安全问题管理列表页面数据统计
const getpageCount = () => {
  safetyAPI.getpageCount({}).then((res) => {
    querybox.value[0].num = res?.pendingProcessingCount; //待处理
    querybox.value[1].num = res?.pendingApprovalCount; //待审核
    querybox.value[3].num = res?.completedCount; //已完成
    querybox.value[2].num = res?.incompleteCount; //已完成
  });
};
// 组件挂载时执行查询
onMounted(async () => {
  getpageCount();
  await loadUserInfo();
  await handleQuery();
  await loadDeptOptions();
  await loadUserList();

  if (props.id) {
    handleOpenDialog(props.id);
  }
});

// 筛选栏显示
const showAdvancedFilters = ref(false);
// 切换筛选栏
const toggleAdvancedFilters = () => {
  showAdvancedFilters.value = !showAdvancedFilters.value;
};
</script>

<style scoped>
/* 状态筛选按钮组样式 */
.status-filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.status-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 90px;
  justify-content: center;
}

/* 当前筛选提示样式 */
.current-filter-tip {
  margin-bottom: 16px;
}

/* 按钮激活状态增强 */
.status-btn:not(.is-plain):not(.is-disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-filter-buttons {
    flex-direction: column;
  }

  .status-btn {
    width: 100%;
    margin-bottom: 8px;
  }
}

/* 激活按钮的特殊样式 */
.status-btn:not(.is-plain) {
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.top_view {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 20px;
}

p {
  margin: 0;
  padding: 0;
}

.top_view_box {
  flex: 1;
  background-color: var(--el-bg-color);
  box-shadow: 0 0 10px 2px rgba(0, 0, 0, 0.1);
  padding: 20px;
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;
}

.top_view_box:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
}

.status-icon {
  transition: all 0.3s ease;
}

.top_view_box div:nth-of-type(1) p:nth-of-type(1) {
  color: #333;
}
.top_view_box div:nth-of-type(1) p:nth-of-type(2) {
  font-weight: bold;
  color: #333;
  font-size: 22px;
  height: 30px;
  line-height: 50px;
}

.rotate-180 {
  transform: rotate(180deg);
}

.advanced-filters {
  margin-top: 1.5rem;
  display: flex;
}
.filters-form-item{
  margin-bottom: 0px;
}
</style>
