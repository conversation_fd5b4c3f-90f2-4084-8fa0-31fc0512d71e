<template>
  <div class="initiate-ticket">
    <h2 class="initiate-ticket_title">发起工单</h2>
    <div v-if="!hasPermission('system:safety:add')" class="waiting-audit">
      <el-card class="waiting-card">
        <Wait />
      </el-card>
    </div>
    <!-- 表单 -->
    <el-form
      v-if="hasPermission('system:safety:add')"
      :model="vulnerabilityForm"
      label-width="140px"
      :rules="rules"
      @submit.prevent="submitForm"
    >
      <!-- 卡片 -->
      <el-card class="mb-4" :disabled="showStep">
        <template #header>
          <div class="card-header">
            <span>工单创建人信息</span>
          </div>
        </template>
        <!-- 工单创建人信息开始 -->
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label-width="70px" label="工号：" prop="employeeId">
              <el-input
                v-model="vulnerabilityForm.employeeId"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label-width="80px" label="创建人：" prop="applicant">
              <el-input disabled>
                <template v-slot:prepend>
                  <Dictmap
                    code="user0x0"
                    v-model="vulnerabilityForm.applicant"
                    :disabled="!showStep"
                  />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label-width="100px" label="所在单位：" prop="deptId">
              <el-input disabled>
                <template v-slot:prepend>
                  <Dictmap
                    code="dept0x0"
                    v-model="vulnerabilityForm.deptId"
                    :disabled="!showStep"
                  />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label-width="100px" label="联系方式：" prop="mobile">
              <el-input
                v-model="vulnerabilityForm.mobile"
                :disabled="!showStep"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
       <!-- 工单创建人基本信息结束 -->
       <el-card class="mb-4" :disabled="showStep">
        <template #header>
          <div class="card-header">
            <span>工单基本信息</span>
          </div>
        </template>
        <!-- 工单基本信息开始 -->
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="工单开始时间：" prop="createTime">
              <el-date-picker
                v-model="vulnerabilityForm.createTime"
                type="datetime"
                placeholder="选择日期时间"
                style="width: 100%"
                disabled
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工单结束时间：" prop="deadline">
              <el-date-picker
                v-model="vulnerabilityForm.deadline"
                type="datetime"
                placeholder="选择日期时间"
                style="width: 100%"
                :disabled="!showStep"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 主要内容 -->
        <!-- 工单基本信息结束 -->
       </el-card>
       <!-- 整改部门开始 -->
        <el-card class="mb-4" :disabled="showStep">
        <template #header>
          <div class="card-header">
            <span>漏洞整改部门</span>
          </div>
        </template>
          <el-row :gutter="24">
          <el-col :span="8">
            
            <el-form-item label="部门名称：">
          <el-tree-select v-model="shstep.deptId" placeholder="请选择所属部门" :data="deptOptions" filterable
            check-strictly :render-after-expand="false"  @change="handleDeptSelectChange"
            :props="{ value: 'value', label: 'label', children: 'children' }" />
        </el-form-item>
          </el-col>
          <el-col :span="8">
             <el-form-item label="联系人：">
              <!-- 树形人员选择器 -->
              <el-select v-model="shstep.id" @change="handleuserSelectChange" clearable>
                <el-option v-for="item in personOptions" :key="item.id"  :value="item.id" :label="item.nickname">
                    <span>{{item.nickname}}--<span v-for="(itemc,index) in item.dutytext" :key="index">{{dutyCache[itemc]}}{{index<item.dutytext.length-1?"，":''}}</span>（--{{item.deptName}}）</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
           <el-col :span="8">
             <el-form-item label="联系方式：">
              <el-input
                v-model="shstep.mobile"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          </el-row>
        </el-card>
       <!-- 整改部门结束 -->
      <!-- 安全工程师信息 -->
      <el-card class="mb-4" :disabled="showStep">
        <template #header>
          <div class="card-header">
          <span>
             安全工程师信息
             <el-tooltip
            placement="top"
            effect="light"
            content="填写工程师基本信息"
            :raw-content="true"
          >
            <el-icon style="vertical-align: -0.15em" size="16">
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
          </span>
          </div>
        </template>
        <!-- 安全工程师信息开始 -->
        <el-form :rules="rules2" :model="SafetyEngineerbox" :inline="true" >
        <div style="display:flex; justify-content: space-between;">
              <el-form-item label-width="110px" label="工程师名称：" prop="engineerName" style="width:20%">
              <el-input  v-model="SafetyEngineerbox.engineerName"></el-input>
            </el-form-item>
            <el-form-item label="联系方式：" label-width="90px" prop="engineerMobile" style="width:20%">
              <el-input   v-model="SafetyEngineerbox.engineerMobile"> </el-input>
            </el-form-item>
            <el-form-item label="微信号：" label-width="80px" prop="engineerWechat" style="width:20%">
              <el-input  v-model="SafetyEngineerbox.engineerWechat"></el-input>
            </el-form-item>
            <el-form-item label="QQ号：" label-width="80px" prop="engineerQq" style="width:20%">
              <el-input  v-model="SafetyEngineerbox.engineerQq"></el-input>
            </el-form-item>
            <el-form-item label="邮箱：" label-width="60px" prop="engineerEmail" style="width:20%">
              <el-input  v-model="SafetyEngineerbox.engineerEmail"></el-input>
            </el-form-item>
        </div>
        </el-form>
        <!-- 安全工程师信息结束 -->
      </el-card>
      <!-- 漏洞列表 -->
      <el-card class="mb-4" :disabled="showStep">
        <template #header>
          <div class="card-header">
            <span>安全漏洞信息</span>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item  label-width="140px" label="信息系统名称：" prop="title">
              <el-input  maxlength="10"
                v-model="vulnerabilityForm.title"
                :disabled="!showStep"
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="信息系统名称：" prop="name">
              <el-input  v-model="vulnerabilityForm.name" maxlength="10"></el-input>
            </el-form-item>
          </el-col> -->
           <el-col :span="12">
            <el-form-item  label-width="140px" label="域名/IP：" prop="domainIp">
             <el-input  v-model="vulnerabilityForm.domainIp"></el-input>
            </el-form-item>
          </el-col>
           <el-col :span="12">
            <el-form-item label-width="140px" label="漏洞来源：" prop="loopholeSource">
              <Dictionary
                v-model="vulnerabilityForm.loopholeSource"
                code="VLUNS_O"
                :disabled="!showStep"
              ></Dictionary>
            </el-form-item>
          </el-col>
          <el-col :span="12">
          <!-- 文件上传 -->
        <el-form-item
          label="安全漏洞报告："
          label-width="140px"
          prop="fileList"
          v-if="showStep"
        >
          <file-upload
            uploadBtnText="上传安全漏洞报告"
            :upload-max-size="20 * 1024 * 1024"
            v-model="fileList"
            :accept="'.pdf,.xls,.doc,.docx,.txt,.csv,.xlsx'"
            :tip="'图片仅支持pdf，excel,word格式，且大小不超过20MB'"
          >
          </file-upload>
          <div>仅支持pdf，excel,word格式的文件，且大小不超过20MB</div>
        </el-form-item>
          </el-col>
        </el-row>
        <el-col :span="24">
            <el-form-item  label-width="140px" label="安全漏洞情况描述：" prop="content">
              <el-input
                type="textarea"
                v-model="vulnerabilityForm.content"
                :rows="2"
                :disabled="!showStep"
              ></el-input>
            </el-form-item>
          </el-col>
          
        <!-- 文件列表 -->
         <el-col :span="24">
          <el-form-item  label-width="140px" label="文件列表" v-if="!showStep">
          <ul class="fileList_ul">
            <li v-for="file in fileList" :key="file.id" >
              <span>{{ file.name }}</span>
              <el-button type="primary" @click="downloadFile(file)"
                >下载</el-button
              >
            </li>
          </ul>
        </el-form-item>
         </el-col>
        <el-col :span="24">
           <p class="base_title">安全漏洞详情：  <el-button type="primary" @click="addVulnerability" v-if="showStep"
              >添加漏洞</el-button
            ></p>
                 <!-- 安全漏洞信息开始 -->
        <el-table
          :data="vulnerabilityList"
          style="width: 100%; "
          border
        >
          <el-table-column
            align="center"
            prop="name"
            label="漏洞名称："
          ></el-table-column>
          <el-table-column
            align="center"
            prop="level"
            label="漏洞级别"
          >
            <template #default="scope">
              <Dictmap v-model="scope.row.level" code="VLUNS_LEVEL" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="ip"
            label="服务器IP"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="url"
            label="URL地址"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="remark"
            label="漏洞描述"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            align="center"
             show-overflow-tooltip
            prop="repairSuggestions"
            label="修复建议"
          ></el-table-column>
          <el-table-column
            align="center"
            width="200"
            prop="fix"
            label="是否修复"
          >
            <template #default="scope">
              <el-tag :type="scope.row.fix === 0 ? 'danger' : 'success'">
                {{ scope.row.fix === 1 ? "已修复" : "未修复" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            width="200"
            prop="createTime"
            label="漏洞创建时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            align="center"
            width="200"
            prop="updateTime"
            label="漏洞更新时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            align="center"
            fixed="right"
            label="操作"
            width="250"
          >
            <template #default="scope">
              <el-button
                @click="openEditDialog(scope.row)"
                type="primary"
                size="small"
                v-if="showStep"
                >编辑</el-button
              >
              <el-button
                @click="deleteVulnerability(scope.row.id)"
                type="danger"
                size="small"
                v-if="showStep"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        </el-col>
        <!-- <p class="base_title">安全漏洞信息</p> -->
       
       
        <!-- 安全漏洞结束 -->
      </el-card>
      <!-- 流程人员配置 -->
      <el-card>
        <safety-process-group
        :shstep="shstep"
          ref="processGroupRef"

          @validation-change="handleValidationChange"
        />
      </el-card>
      <!-- 表单操作按钮 -->
      <div class="form-actions" v-if="showStep">
        <el-button
          type="primary"
          @click="submitForm"
          v-hasPerm="['system:safety:add']"
          >提交工单</el-button
        >
         <el-button type="info" @click="concelForm" >取消</el-button>
        <el-button @click="resetForm">重置</el-button>
      </div>
    </el-form>
    <!-- 编辑漏洞弹窗 -->
    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      append-to-body
      @close="handleCloseDialog"
    >
      <el-form
        ref="currentVulnerabilityFormRef"
        :model="currentVulnerability"
        :rules="rules1"
        label-width="120px"
      >
        <el-form-item label="漏洞名称" prop="name">
          <el-input v-model="currentVulnerability.name"></el-input>
        </el-form-item>
        <el-form-item label="漏洞级别" prop="level">
          <dictionary v-model="currentVulnerability.level" code="VLUNS_LEVEL" />
        </el-form-item>
        <el-form-item label="服务器IP" prop="ip">
          <el-input v-model="currentVulnerability.ip"></el-input>
        </el-form-item>
        <el-form-item label="URL地址" prop="url">
          <el-input v-model="currentVulnerability.url"></el-input>
        </el-form-item>
          <el-form-item label="漏洞描述" prop="remark">
          <el-input
            type="textarea"
            v-model="currentVulnerability.remark"
          ></el-input>
        </el-form-item>
        <el-form-item label="修复建议" prop="repairSuggestions">
          <el-input
            type="textarea"
            v-model="currentVulnerability.repairSuggestions"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="saveVulnerability">确 定</el-button>
          <el-button @click="handleCloseDialog">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 用户表单弹窗 -->
    <UserDialog
      v-model:visible="dialogAddUser.visible"
      :userId="dialogAddUser.userId"
    />
    <!-- 选择资产弹窗 -->
    <select-assets
      v-model:visible="transferDialog.visible"
      :title="'选择关联资产'"
      :selected-assets="transferDialog.selectedAssets"
      :selected-assets-data="sleectedAssetsData"
      :single-select="true"
      @selected="handleAssetsSelected"
    />
  </div>
</template>

<script lang="ts" setup>
import { formatLocalDateTime } from "@/utils/dateUtils";
import FileUpload from "@/components/Upload/FileUpload.vue";
import DeptAPI from '@/api/dept'
import { useDictStore } from '@/store/modules/dictStore'
import {
  TicketInfo,
  StepStatus,
  StepPermission,
} from "@/api/work_management/safety/pocess";
import safetyAPI, {
  safetyForm,
  VulnerabilityForm,
} from "@/api/work_management/safety";
import { hasAuth } from "@/plugins/permission";
import Wait from "@/views/error-page/401.vue";
import UserAPI from "@/api/user";
import UserDialog from "@/views/system/user/components/UserDialog.vue";
import SafetyProcessGroup from "@/components/ProcessGroup/SafetyProcessGroup.vue";
import SelectAssets from "@/components/AssetsManage/SelectAssets.vue";
import assetsAPI from "@/api/assets_management/details/assets";
import { useRouter, useRoute } from "vue-router";
import { json } from "stream/consumers";
import { Console } from "console";
const router = useRouter();
const dictStore = useDictStore()

interface TicketData {
  id: number;
  currentStep: string;
  isClick: boolean;
}
const processGroupRef = ref();
const currentVulnerabilityFormRef = ref(ElForm);
// 新增系统弹窗
const dialogAddSystem = reactive({
  title: "",
  visible: false,
  id: undefined,
});

// 新增用户弹窗
const dialogAddUser = reactive({
  visible: false,
  userId: undefined,
});

// 定义资产选择弹窗状态
const transferDialog = reactive({
  visible: false,
  selectedAssets: [] as number[], // 已选择的资产ID数组
});

const props = defineProps<{
  ticketdata: TicketData;
}>();

const emit = defineEmits(["next"]);
const ID = ref(props.ticketdata ? props.ticketdata.id : null);
// 表单数据
const vulnerabilityForm = reactive<safetyForm>({
  status: 0,
  createTime: new Date(),
  fileList: [],
  deadline: (() => {
    const date = new Date();
    date.setDate(date.getDate() + 3); // 设置为3天后
    return date;
  })(),
});
const stepPermission = ref<StepPermission | null>(null);

const currentStep = ref(props.ticketdata.currentStep);
const showStep = ref(true);
const nowStep = ref("");
const stepStatus = ref<StepStatus | null>(null);

// 是否为当前步骤
function isCurrentStep() {
  console.log(currentStep.value);
  if (currentStep.value == nowStep.value) {
    //是当前步骤
    showStep.value = true;
  } else {
    showStep.value = false;
  }
}

// 文件列表
const fileList = ref([] as any[]);

// 漏洞列表数据
const vulnerabilityList = ref<Array<VulnerabilityForm>>([]);

/**  弹窗对象  */
const dialog = reactive({
  visible: false,
  title: "",
});

/** 关闭弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  if(vulnerabilityList.value.length>0 && (!vulnerabilityList.value[vulnerabilityList.value.length-1].name || vulnerabilityList.value[vulnerabilityList.value.length-1].name=='')){
  vulnerabilityList.value.splice(vulnerabilityList.value.length-1,1)
  }
}

// 当前编辑的漏洞信息
const currentVulnerability = reactive({
  id: "",
  name: "",
  level: "",
  ip: "",
  url: "",
  remark: "",
  fix: 0,
  createTime: formatLocalDateTime(new Date(), "datetime"), // 设置为当前时间
  updateTime: formatLocalDateTime(new Date(), "datetime"), // 设置为当前时间
});

// 添加漏洞
const addVulnerability = () => {
  const index = vulnerabilityList.value.length + 1;
  vulnerabilityList.value.push({
    id: Number(generateTimeBasedId() + "" + index),
    name: "",
    level: "0",
    ip: "",
    url: "",
    remark: "",
    fix: 0,
    createTime: formatLocalDateTime(new Date(), "datetime"), // 设置为当前时间
    updateTime: formatLocalDateTime(new Date(), "datetime"), // 设置为当前时间
  });
  openEditDialog(vulnerabilityList.value[vulnerabilityList.value.length - 1]);
};

// 附件下载
const downloadFile = (row: any) => {
  const fileUrl = row.url ? row.url : null; // 添加 null check
   if (row.url) {
  fetch(row.url)
  .then(response => response.blob())
  .then(blob => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = row.name; // 设置自定义文件名
    link.style.display = 'none'; // 隐藏链接
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  })
  .catch(error => {
      ElMessage.error('附件不存在');
  }); 
  } else {
    ElMessage.error("附件不存在");
  }
};

// 获取资产类型名称
const getAssetTypeName = (type: number) => {
  const typeMap: Record<number, string> = {
    "-1": "未知类型",
    1: "服务器",
    2: "网络设备",
    3: "安全设备",
    10: "信息系统",
  };
  return typeMap[type] || "未知类型";
};

// <!--选择资产弹窗-->
const sleectedAssetsData = ref<any[]>([]);

// 计算属性: 获取选中的资产详细信息
const assetsData = computed(() =>
  sleectedAssetsData.value.filter((asset) =>
    transferDialog.selectedAssets.includes(asset.id)
  )
);

// 3. 打开弹窗方法
const openTransferDialog = () => {
  transferDialog.visible = true;

  // 如果已有选择的资产但没有详细数据，尝试获取详细数据
  if (
    transferDialog.selectedAssets.length > 0 &&
    sleectedAssetsData.value.length === 0
  ) {
    loadSelectedAssetsData();
  }
};

// 加载已选资产的详细数据
const loadSelectedAssetsData = async () => {
  if (transferDialog.selectedAssets.length === 0) return;
};

// 处理资产选择结果，完善逻辑
const handleAssetsSelected = ({ selectedIds, selectedAssets }: any) => {
  transferDialog.selectedAssets = selectedIds;
  sleectedAssetsData.value = selectedAssets;

  // 将选中的资产ID保存到表单中
  vulnerabilityForm.assetIds = transferDialog.selectedAssets;

  // 如果选择了资产，自动配置流程组件中的整改部门
  if (selectedAssets) {
    const selectedAsset = selectedAssets[0]; // 只有一个资产
    autoConfigProcessGroup(selectedAsset);
  }
};

// 自动配置流程组件中的整改部门
const autoConfigProcessGroup = async (asset: any) => {
  if (!processGroupRef.value) {
    console.warn("流程组件引用不存在");
    return;
  }

  try {
    // 获取资产的管理部门和管理员信息
    const assetDeptId = asset.deptId;
    const assetManagerName = asset.ownerName || asset.managerName;
    let assetDeptName = asset.deptName;

    console.log("自动配置流程组件:", {
      assetDeptId,
      assetManagerName,
      assetDeptName,
      asset,
    });

    // 确保有必要的信息
    if (!assetDeptId || !assetManagerName) {
      ElMessage.warning("资产缺少必要的部门或管理员信息，无法自动配置");
      return;
    }

    // 直接调用流程组件的自动配置方法（强制重新配置）
    await processGroupRef.value.autoConfigRemediationDept({
      deptId: assetDeptId,
      deptName: assetDeptName || `部门${assetDeptId}`,
      managerName: assetManagerName,
      asset: asset,
      forceUpdate: true, // 强制更新标志
    });

    // 等待DOM更新
    await nextTick();

    // 延迟验证以确保数据完全同步
    setTimeout(() => {
      const validation = processGroupRef.value.getValidationStatus();
      console.log("自动配置后的验证状态:", validation);
    }, 500);
  } catch (error) {
    console.error("自动配置流程组件失败:", error);
    ElMessage.error("自动配置整改部门失败");
  }
};

// 移除选中的资产
const removeSelectedAsset = (id: number) => {
  transferDialog.selectedAssets = transferDialog.selectedAssets.filter(
    (assetId) => assetId !== id
  );
  sleectedAssetsData.value = sleectedAssetsData.value.filter(
    (asset) => asset.id !== id
  );
  vulnerabilityForm.assetIds = transferDialog.selectedAssets;
};

//rule
const rules = reactive({
  id: [{ required: true, message: "请输入安全问题工单id", trigger: "blur" }],
  title: [{ required: true, message: "请输入标题", trigger: "blur" }],
  applicant: [{ required: true, message: "请输入申请人", trigger: "blur" }],
  mobile: [{ required: false, message: "请输入手机号", trigger: "blur" }],
  content: [
    { required: false, message: "请输入工单概述内容", trigger: "blur" },
  ],
  createTime: [
    { required: true, message: "请输入工单提交时间", trigger: "blur" },
  ],
  deadline: [
    { required: false, message: "请输入要求整改时间", trigger: "blur" },
  ],
  deptId: [{ required: true, message: "请输入申请人部门id", trigger: "blur" }],
  errStep: [{ required: false, message: "请输入未通过步骤", trigger: "blur" }],
  loopholeSource: [
    { required: true, message: "请输入漏洞来源", trigger: "blur" },
  ],
  status: [{ required: false, message: "请输入状态", trigger: "blur" }],
  step: [{ required: false, message: "请输入当前步骤", trigger: "blur" }],
  systemId: [{ required: false, message: "请输入系统id", trigger: "blur" }],
  updatedTime: [
    { required: false, message: "请输入工单更新时间", trigger: "blur" },
  ],
});

//rule
const rules1 = reactive({
  id: [{ required: true, message: "请输入漏洞id", trigger: "blur" }],
  name: [{ required: true, message: "请输入漏洞名称", trigger: "blur" }],
  level: [{ required: true, message: "请输入漏洞级别", trigger: "blur" }],
  ip: [{ required: false, message: "请输入服务器IP", trigger: "blur" }],
  url: [{ required: false, message: "请输入URL地址", trigger: "blur" }],
  remark: [{ required: false, message: "请输入漏洞描述", trigger: "blur" }],
  repairSuggestions: [{ required: false, message: "请输入修复建议", trigger: "blur" }],
});
const rules2=reactive({
  engineerName: [{ required: true, message: "请输入工程师名称", trigger: "blur" }],
});

// 流程验证状态
const processValidationStatus = ref<{
  valid: boolean;
  missingSteps: any[];
} | null>(null);

// 处理验证状态变化
const handleValidationChange = (status: any) => {
  processValidationStatus.value = status;
};

// 检查流程配置
const checkProcessConfiguration = () => {
  if (processGroupRef.value) {
    processValidationStatus.value = processGroupRef.value.getValidationStatus();
    return processValidationStatus.value?.valid;
  }
  return false;
};

// 打开编辑弹窗
const openEditDialog = (vulnerability: TicketInfo) => {
  dialog.visible = true;
  dialog.title = "编辑漏洞";

  Object.assign(currentVulnerability, vulnerability);
};

// 保存漏洞信息
const saveVulnerability = () => {
  currentVulnerabilityFormRef.value.validate((valid: any) => {
    if (valid) {
      const index = vulnerabilityList.value.findIndex(
        (v) => v.id == currentVulnerability.id
      );
      if (index !== -1) {
        Object.assign(vulnerabilityList.value[index], currentVulnerability);
      }
      currentVulnerabilityFormRef.value.resetFields();
      currentVulnerabilityFormRef.value.clearValidate();
      dialog.visible = false;
      console.log(vulnerabilityList.value);
    }
  });
};

// 删除漏洞
const deleteVulnerability = (id: number) => {
  vulnerabilityList.value = vulnerabilityList.value.filter((v) => v.id !== id);
};

// 生成基于当前时间的工单编号
const generateTimeBasedId = () => {
  const now = new Date();
  return `${now.getFullYear()}${(now.getMonth() + 1)
    .toString()
    .padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}${now
    .getHours()
    .toString()
    .padStart(2, "0")}${now.getMinutes().toString().padStart(2, "0")}${now
    .getSeconds()
    .toString()
    .padStart(2, "0")}`;
};
// 提交表单
const submitForm = async () => {
  // 判断是否是新增
     if(SafetyEngineerbox.value.id){
      updateSafetyEngineerConfig()
      vulnerabilityForm.engineerId=SafetyEngineerbox.value.id
      vulnerabilityForm.engineerName=SafetyEngineerbox.value.engineerName
      vulnerabilityForm.engineerMobile=SafetyEngineerbox.value.engineerMobile
      vulnerabilityForm.engineerWechat=SafetyEngineerbox.value.engineerWechat
      vulnerabilityForm.engineerQq=SafetyEngineerbox.value.engineerQq
      vulnerabilityForm.engineerEmail=SafetyEngineerbox.value.engineerEmail
    }else{
      if(SafetyEngineerbox.value.engineerName!=''){
         saveSafetyEngineerConfig()
         vulnerabilityForm.engineerName=SafetyEngineerbox.value.engineerName
        vulnerabilityForm.engineerMobile=SafetyEngineerbox.value.engineerMobile
        vulnerabilityForm.engineerWechat=SafetyEngineerbox.value.engineerWechat
        vulnerabilityForm.engineerQq=SafetyEngineerbox.value.engineerQq
        vulnerabilityForm.engineerEmail=SafetyEngineerbox.value.engineerEmail
      }
  }
  // 获取流程组件中的数据
  if (!checkProcessConfiguration()) {
    console.log(processValidationStatus.value?.missingSteps)
    // 获取未配置步骤的名称列表
    const missingSteps = processValidationStatus.value?.missingSteps || [];
    const missingStepNames = missingSteps
      .map((step) => step?.name || "未知步骤")
      .filter(Boolean)
      .join("、");

    ElMessage.error(`请配置流程步骤: ${missingStepNames}`);
    return;
  }
  const processData = processGroupRef.value?.getProcessData();

  vulnerabilityForm.vulns = vulnerabilityList.value;
  vulnerabilityForm.assetIds = transferDialog.selectedAssets;
  vulnerabilityForm.fileIds = fileList.value.map((file) => file.id);
  vulnerabilityForm.status = 0;
  vulnerabilityForm.createTime = formatLocalDateTime(
    new Date(vulnerabilityForm.createTime)
  );
  vulnerabilityForm.updateTime = formatLocalDateTime(new Date());
  vulnerabilityForm.deadline = formatLocalDateTime(
    new Date(vulnerabilityForm.deadline)
  );

  // 如果存在流程数据，添加到表单中
  if (processData) {
    vulnerabilityForm.reviewProcessForms =
      convertToReviewProcessForms(processData);
  } else {
    ElMessage.error("请选择流程处理人员");
    return;
  }

  if (flag) {
    vulnerabilityForm.id = flag;
  }

  await ElMessageBox.confirm("确定要提交漏洞工单吗？", "确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  });

  try {
    if (flag) {
      
      await safetyAPI.update(flag, vulnerabilityForm);
      ElMessage.success("漏洞工单已更新");
    } else {
      await safetyAPI.add(vulnerabilityForm);
      ElMessage.success("漏洞工单已提交");
    }
    emit("next", props.ticketdata.id);
  } catch (error) {
    console.error("Error submitting vulnerability ticket:", error);
    ElMessage.error("提交失败，请重试");
  }
};

// 将前端流程数据转换为后端需要的格式
const convertToReviewProcessForms = (processData: any) => {
  const result: any = [];
  const now = formatLocalDateTime(new Date());

  try {
    // 处理执行列表
    if (processData.executionList && processData.executionList.length > 0) {
      processData.executionList.forEach((item) => {
        const departmentId = item.departmentId; // 流程步骤 ID

        // 转换部门类型映射 (1->2, 2->3, 3->4, 4->5)
        // 根据文档，部门类型已改变：2工单审核部门 3安全整改部门 4工单复核部门 5工单评价部门
        const deptTypeMap = { 1: "2", 2: "3", 3: "4", 4: "5" };
        const deptType = deptTypeMap[departmentId] || departmentId.toString();

        const selectedDeptId = item.selectedDeptId || 0; // 选择的部门ID
        const persons = processData.executionPersons?.[departmentId] || [];

        // 只有选择了部门和人员才进行提交
        if (selectedDeptId && persons.length > 0) {
          // 将多个用户ID合并为逗号分隔的字符串
          const userIds = persons.map((person) => person.id).join(",");

          result.push({
            id: null, // 使用原有ID或0
            businessType: "safety", // 固定值
            businessId: vulnerabilityForm.id ? Number(vulnerabilityForm.id) : 0,
            executeDeptType: deptType, // 部门类型: 2-5
            executeType: "1", // 执行类型: 1执行
            deptId: selectedDeptId, // 选择的具体部门ID
            userId: userIds, // 多个用户ID以逗号分隔
            enableSms: item.enableSms ? "1" : "0",
            smsTemplateId: item.smsTemplateId || 0,
            notifyType: item.notifyType || "once",
            notifyPeriod: item.notifyPeriod || "daily",
            createTime: now,
            updateTime: now,
          });
        }
      });
    }

    // 处理通知列表
    if (
      processData.notificationList &&
      processData.notificationList.length > 0
    ) {
      processData.notificationList.forEach((item) => {
        const departmentId = item.departmentId; // 流程步骤 ID

        // 转换部门类型映射 (1->2, 2->3, 3->4, 4->5)
        const deptTypeMap = { 1: "2", 2: "3", 3: "4", 4: "5" };
        const deptType = deptTypeMap[departmentId] || departmentId.toString();

        const selectedDeptId = item.selectedDeptId || 0; // 选择的部门ID
        const persons = processData.notificationPersons?.[departmentId] || [];

        // 只有选择了部门和人员才进行提交
        if (selectedDeptId && persons.length > 0) {
          // 将多个用户ID合并为逗号分隔的字符串
          const userIds = persons.map((person) => person.id).join(",");

          result.push({
            id: null, // 使用原有ID或0
            businessType: "safety", // 固定值
            businessId: vulnerabilityForm.id ? Number(vulnerabilityForm.id) : 0,
            executeDeptType: deptType, // 部门类型: 2-5
            executeType: "2", // 执行类型: 2通知
            deptId: selectedDeptId, // 选择的具体部门ID
            userId: userIds, // 多个用户ID以逗号分隔
            enableSms: item.enableSms ? "1" : "0",
            smsTemplateId: item.smsTemplateId || 0,
            notifyType: item.notifyType || "once",
            notifyPeriod: item.notifyPeriod || "daily",
            createTime: now,
            updateTime: now,
          });
        }
      });
    }
    // console.log('转换后的流程数据:', result);
    return result;
  } catch (error) {
    console.error("转换流程数据出错:", error);
    return [];
  }
};

// 重置表单
const resetForm = () => {
  vulnerabilityForm.id = generateTimeBasedId();
  vulnerabilityForm.mobile = "";
  vulnerabilityForm.systemId = undefined;
  vulnerabilityForm.loopholeSource = undefined;
  vulnerabilityForm.title = "";
  vulnerabilityForm.content = "";
  vulnerabilityForm.remarks = "";
  fileList.value = [];
  vulnerabilityList.value = [];
  transferDialog.selectedAssets = [];
  sleectedAssetsData.value = [];
  processGroupRef.value?.reset();
};
// 取消
const concelForm=()=>{
 router.push({
      path: '/work_management/safety', 
      query: { refresh: Date.now() } 
    });
}
interface  SafetyEngineer{
  id?:  any;
  engineerName?: string;
  engineerMobile: string;
  engineerWechat: string;
  engineerQq:string;
  engineerEmail: string;
}
const SafetyEngineerbox:any = ref<SafetyEngineer>(
  {
    id:null,
    engineerName:'',
    engineerMobile: '',
    engineerWechat: '',
    engineerQq: '',
    engineerEmail: '',
  },
);

// 新增安全工程师
const saveSafetyEngineerConfig = async () => {
  console.log(SafetyEngineerbox.value)
  const statusRes = await safetyAPI.saveSafetyEngineerConfig(SafetyEngineerbox.value)
}
// 修改安全工程师
const updateSafetyEngineerConfig = async () => {
  const statusRes = await safetyAPI.updateSafetyEngineerConfig(SafetyEngineerbox.value.id,SafetyEngineerbox.value)
}
// 获取安全工程师信息
const SafetyEngineerConfig = async () => {
  const statusRes = await safetyAPI.getSafetyEngineerConfig({})
  SafetyEngineerbox.value=statusRes[0] ||{}
}
// 部门
const deptOptions = ref<any>([])
const isExecutionList = ref(true)
// 人员
const dutyCache = ref<Record<string, string>>({}) 
const personOptions = ref<any[]>([])
const shstep=ref({})

const loadDutyCache = async () => {
      // console.log('加载DUTY字典缓存')
      const options = await dictStore.fetchOptions('DUTY')
      // console.log('加载DUTY字典选项:', options)
      options.forEach(item => {
        dutyCache.value[item.value] = item.label
      })
      
}


// 处理部门选择变更
const handleDeptSelectChange = async (value: number,id) => {
  console.log("--------",value)
  shstep.value.id=''
  const response = await UserAPI.getPage({
      deptId:value,
      pageNum: 1,
      pageSize: 100
    })
    personOptions.value=JSON.parse(JSON.stringify(response.list)) 
    personOptions.value.forEach((item)=>{
      let myduty=item.duty && item.duty!=''? JSON.parse(JSON.stringify( item.duty)):''

        item.dutytext=myduty  && myduty!=''?myduty.split(","):''
    })
    loadDutyCache()
    if(id){
      handleuserSelectChange(id)
    }

}
const handleuserSelectChange  = (value) => {
  console.log( personOptions.value)
  const foundItem = personOptions.value.find(item => item.id == value);
  shstep.value=JSON.parse(JSON.stringify(foundItem))
  console.log(foundItem)

}

onMounted(() => {
   DeptAPI.getOptions().then((data) => {
    deptOptions.value = data
  })
  handleQuery();
  SafetyEngineerConfig();
});

let flag: string | number | undefined = undefined;

async function handleQuery() {
  console.log(ID.value);
  await getUserInfo();
  if (ID.value) {
    // 编辑模式：加载已有数据
   
    await safetyAPI.getFormData(ID.value ?? 0).then((data) => {
      Object.assign(vulnerabilityForm, data);
      const foundItem = vulnerabilityForm.reviewProcessForms?.find(item => item.executeDeptType == 3);
       handleDeptSelectChange(foundItem.deptId,foundItem.userId)
        vulnerabilityList.value = vulnerabilityForm.vulns || [];
        flag = vulnerabilityForm.id;
      // 处理资产数据回填
      if (vulnerabilityForm.assetIds && vulnerabilityForm.assetIds.length > 0) {
        transferDialog.selectedAssets = [...vulnerabilityForm.assetIds];
        // 加载资产详情数据（编辑模式下不触发自动配置）
        loadSelectedAssetsDataFromIds(vulnerabilityForm.assetIds);
      }
    });

    const statusRes: any = await safetyAPI.getStepStatus(props.ticketdata.id);
    stepStatus.value = statusRes;
    for (const step in stepStatus.value) {
      if (stepStatus.value[step as keyof StepStatus] == "process") {
        nowStep.value = step as keyof StepStatus;
        break;
      }
    }
    fileList.value = vulnerabilityForm.fileList || [];

    // 重要：在资产数据加载完成后再处理流程数据
    await nextTick();
    await getProcessData(vulnerabilityForm); // 映射已有的流程数据
  } else {
    // 新建模式：应用默认配置
    await nextTick();
    if (processGroupRef.value) {
      console.log("新建工单，应用默认配置");
      try {
        // 调用流程组件的应用默认配置方法
        await processGroupRef.value.applyDefaultConfig();
        console.log("默认配置应用成功");
      } catch (error) {
        console.error("应用默认配置失败:", error);
      }
    }
  }

  if (props.ticketdata.isClick) {
    isCurrentStep();
  }

  // 只在新建工单时生成新ID，修改退回工单时保留原ID
  if (!flag) {
    vulnerabilityForm.id = generateTimeBasedId();
  }
}

// 添加此方法来根据ID加载资产详情
const loadSelectedAssetsDataFromIds = async (assetIds) => {
  if (!assetIds || assetIds.length === 0) return;

  try {
    // 如果工单中已经包含assetsList，直接使用它
    if (
      vulnerabilityForm.assetsList &&
      vulnerabilityForm.assetsList.length > 0
    ) {
      sleectedAssetsData.value = vulnerabilityForm.assetsList.map((asset) => ({
        id: asset.id,
        name: asset.name,
        type: asset.type,
        status: asset.status,
        ownerName: asset.ownerName || asset.managerName,
        deptId: asset.deptId,
        url: asset.url,
        deptName: asset.deptName,
      }));
    }
  } catch (error) {
    console.error("加载已选资产详情失败:", error);
  }
};

//获取当前用户信息 填充name,employeeId,department,applicant
const getUserInfo = async () => {
  const data = await UserAPI.getProfile();
  vulnerabilityForm.applicant = data.id;
  vulnerabilityForm.employeeId = data.username;
  vulnerabilityForm.deptId = data.deptId;
  vulnerabilityForm.mobile = data.mobile || "";
};

// 映射流程数据
const getProcessData = async (data) => {
  console.log("映射流程数据:", data);
  try {
    // 检查是否有流程数据
    if (!data.reviewProcessForms || !Array.isArray(data.reviewProcessForms)) {
      console.log("未找到有效的流程数据");
      return;
    }

    console.log("原始流程数据:", data.reviewProcessForms);

    // 准备执行和通知列表数据
    const execList = [];
    const notifyList = [];

    // 准备人员数据的映射
    const execPersonsMap = {};
    const notifyPersonsMap = {};

    // 遍历流程数据
    for (const process of data.reviewProcessForms) {
      // 确定是执行部门还是通知部门
      const isExecution = process.executeType === "1";

      // 映射逆向转换 (2->1, 3->2, 4->3, 5->4)
      const deptTypeReverseMap = { "2": 1, "3": 2, "4": 3, "5": 4 };
      const deptTypeNum =
        deptTypeReverseMap[process.executeDeptType] ||
        Number(process.executeDeptType);

      // 解析逗号分隔的用户ID列表
      const userIdList = process.userId
        ? process.userId.split(",").map((id) => id.trim())
        : [];

      // 获取部门名称
      let deptName = "";
      try {
        const deptOptions = await dictStore.fetchOptions("dept0x0");
        const findDeptName = (options: any[], id: number): string => {
          for (const option of options) {
            if (option.value === id) {
              return option.label;
            }
            if (option.children && option.children.length > 0) {
              const found = findDeptName(option.children, id);
              if (found) return found;
            }
          }
          return "";
        };
        deptName =
          findDeptName(deptOptions, process.deptId) || `部门${process.deptName}`;
      } catch (error) {
        deptName = `部门${process.deptName}`;
      }

      // 创建基础项目
      const baseItem = {
        id: process.id || 0,
        departmentId: deptTypeNum,
        selectedDeptId: process.deptId || 0,
        deptName: deptName,
        personName: "", // 稍后设置
        enableSms: process.enableSms === "1",
        smsTemplateId: process.smsTemplateId
          ? Number(process.smsTemplateId)
          : undefined,
        smsContent: "",
        notifyType: process.notifyType || "once",
        notifyPeriod: process.notifyPeriod || "daily",
        userIds: userIdList,
      };

      // 获取用户详细信息
      const userInfoArray = [];
      if (userIdList.length > 0) {
        for (const userId of userIdList) {
          try {
            const userResponse = await UserAPI.getFormData(Number(userId));
            if (userResponse) {
              const userInfo = {
                id: userResponse.id || userId,
                username: userResponse.username || "",
                nickname: userResponse.nickname || `用户${userId}`,
                deptName: deptName,
                mobile: userResponse.mobile || "",
              };
              userInfoArray.push(userInfo);
            }
          } catch (error) {
            console.warn(`获取用户详情失败 ID:${userId}`, error);
            // 添加基本用户信息作为备选
            userInfoArray.push({
              id: userId,
              username: "",
              nickname: `用户${userId}`,
              deptName: deptName,
              mobile: "",
            });
          }
        }
      }

      // 设置人员名称
      baseItem.personName =
        userInfoArray.map((u) => u.nickname).join("、") || "未设置";

      // 根据类型添加到不同列表
      if (isExecution) {
        // 检查执行列表是否已有相同部门类型的项
        const existingIndex = execList.findIndex(
          (item) => item.departmentId === deptTypeNum
        );
        if (existingIndex === -1) {
          execList.push(baseItem);
        }

        // 添加用户到执行人员映射
        if (userInfoArray.length > 0) {
          execPersonsMap[deptTypeNum] = userInfoArray;
        }
      } else {
        // 检查通知列表是否已有相同部门类型的项
        const existingIndex = notifyList.findIndex(
          (item) => item.departmentId === deptTypeNum
        );
        if (existingIndex === -1) {
          notifyList.push(baseItem);
        }

        // 添加用户到通知人员映射
        if (userInfoArray.length > 0) {
          notifyPersonsMap[deptTypeNum] = userInfoArray;
        }
      }
    }

    // 更新表单数据
    vulnerabilityForm.executionList = execList;
    vulnerabilityForm.notificationList = notifyList;
    vulnerabilityForm.executionPersons = execPersonsMap;
    vulnerabilityForm.notificationPersons = notifyPersonsMap;

    console.log("映射完成的数据:", {
      executionList: execList,
      notificationList: notifyList,
      executionPersons: execPersonsMap,
      notificationPersons: notifyPersonsMap,
    });

    // 初始化流程组件数据
    await nextTick();
    if (processGroupRef.value) {
      processGroupRef.value.initFromExistingData({
        executionList: execList,
        notificationList: notifyList,
        executionPersons: execPersonsMap,
        notificationPersons: notifyPersonsMap,
      });

      // 重要：如果整改部门已配置，需要重新触发自动配置以确保界面正确显示
      const remediationDeptConfig = execPersonsMap[2]; // 整改部门对应索引2
      if (
        remediationDeptConfig &&
        remediationDeptConfig.length > 0 &&
        sleectedAssetsData.value.length > 0
      ) {
        console.log("重新触发整改部门自动配置");
        // 延迟一下确保组件完全初始化
        setTimeout(() => {
          autoConfigProcessGroup(sleectedAssetsData.value[0]);
        }, 200);
      }
    }
  } catch (error) {
    console.error("流程数据映射出错:", error);
  }
};

// 权限判断
function hasPermission(requiredPerms: string): boolean {
  console.log(hasAuth(requiredPerms, "button"));
  return hasAuth(requiredPerms, "button");
}
</script>

<style scoped>
.asset-selection-item {
  margin-bottom: 16px;
}

.asset-selection-wrapper {
  width: 100%;
}

.asset-button-container {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  align-items: center;
}
/* 资产基本信息 */
.asset-select-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.asset-select-box div {
  display: flex;
  align-items: center;
}
.asset-select-box div p {
  font-size: 13px;
}
.asset-select-box div p span {
  font-size: 12px;
  color: #666;
}
.asset-select-btn {
  /* margin-left: 20px; */
  min-width: 140px;
  color: #409eff;
  cursor: pointer;
}
.selected-assets-container {
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding: 12px;
  margin-top: 8px;
}

.assets-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.assets-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 6px;
}

.assets-title svg {
  color: var(--el-color-primary);
  font-size: 16px;
}

.modify-btn {
  font-size: 12px;
  padding: 2px 8px;
}

.assets-table {
  border-radius: 4px;
  overflow: hidden;
}

.asset-type-tag {
  font-weight: normal;
}

.no-assets-tip {
  text-align: center;
  background: var(--el-bg-color-page);
  border: 1px dashed var(--el-border-color-light);
  border-radius: 4px;
  margin-top: 4px;
}

.no-assets-tip :deep(.el-empty__description) {
  color: var(--el-text-color-regular);
  font-size: 13px;
}

/* 表格条纹样式美化 - 使用主题变量 */
:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: var(--el-fill-color-lighter);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .asset-button-container {
    flex-direction: column;
    align-items: stretch;
  }

  .asset-select-btn,
  .asset-clear-btn {
    width: 100%;
    margin-bottom: 8px;
  }

  .assets-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-actions {
  margin-top: 20px;
  text-align: center;
}
.initiate-ticket_title {
  margin-top: 0;
}
.base_title {
  font-weight: bold;
  margin: 0;
  padding: 0;
  height: 64px;
  line-height: 64px;
  font-size: 14px !important;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #666;
}
:deep(.el-form-item) {
  margin-right: 0;
}
.fileList_ul{
  width: 100%;
}
 .fileList_ul li{
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f7f6f6;
  padding: 5px;
  border-radius: 4px;
  margin-bottom: 10px;
  }
</style>
