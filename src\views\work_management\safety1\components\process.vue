<template>
  <div class="process">
    <!-- 简化的步骤条 - 仅显示，不可点击 -->
    <el-steps :active="getStepIndex" finish-status="success" process-status="process" simple>
      <el-step 
        v-for="(status, step) in stepStatus" 
        :key="step" 
        :title="stepTitles[step]" 
        :class="getStepClass(status)"
      >
        <template #title>
          <div class="step-info">
            <span>{{ stepTitles[step] }}</span>
          </div>
        </template>
      </el-step>
    </el-steps>
    <!-- 查看流转记录按钮 -->
    <div class="record-action">
      <el-button 
        type="primary" 
        @click="showTransferRecord"
        plain
        style="font-size:16px;"
      >
        <el-icon><Document /></el-icon>
       <span style="margin-left:5px"> 查看流转记录</span>
      </el-button>
    </div>

    <!-- 流转信息展示卡片 -->
    <transfer-record-dialog
      v-model:visible="showRecordsDialog"
      :ticket-id="props.ticketId"
      :api-type="'safety'"
      :show-attachments="true"
      ref="transferRecordRef"
    />

    <!-- 步骤内容 -->
    <div class="step-content">
      <InitiateTicket 
        v-if="currentStep == 'initiateTicket'" 
        v-model:ticketdata="ticketdata" 
        @next="nextStep" 
      />
      <VulnerabilityAudit 
        v-if="currentStep == 'vulnerabilityAudit'" 
        v-model:ticketdata="ticketdata" 
        @next="nextStep"
      />
      <VulnerabilityFix 
        v-if="currentStep == 'vulnerabilityFix'" 
        v-model:ticketdata="ticketdata" 
        @next="nextStep" 
      />
      <FixVerification 
        v-if="currentStep == 'fixVerification'" 
        v-model:ticketdata="ticketdata" 
        @next="nextStep" 
      />
      <FixEvaluation 
        v-if="currentStep == 'fixEvaluation'" 
        v-model:ticketdata="ticketdata" 
        @next="nextStep" 
      />
      <CloseTicket 
        v-if="currentStep == 'closeTicket'" 
        v-model:ticketdata="ticketdata" 
        @next="nextStep" 
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import safetyAPI from '@/api/work_management/safety/index'
import InitiateTicket from './ProcessSteps/InitiateTicket.vue'
import VulnerabilityAudit from './ProcessSteps/VulnerabilityAudit.vue'
import VulnerabilityFix from './ProcessSteps/VulnerabilityFix.vue'
import FixVerification from './ProcessSteps/FixVerification.vue'
import FixEvaluation from './ProcessSteps/FixEvaluation.vue'
import CloseTicket from './ProcessSteps/CloseTicket.vue'
import TransferRecordDialog from '@/components/GeneralModel/TransferRecord.vue'

const props = defineProps({
  ticketId: Number
})

const emit = defineEmits(['init'])

// 步骤配置
const stepTitles = {
  initiateTicket: '发起工单',
  vulnerabilityAudit: '漏洞审核',
  vulnerabilityFix: '漏洞整改',
  fixVerification: '漏洞复核',
  fixEvaluation: '整改评价',
  closeTicket: '关闭工单'
}

// 响应式数据
const transferRecordRef = ref()
const showRecordsDialog = ref(false)
const currentStep = ref('initiateTicket')
const stepStatus = ref({
  initiateTicket: 'process',
  vulnerabilityAudit: 'wait',
  vulnerabilityFix: 'wait',
  fixVerification: 'wait',
  fixEvaluation: 'wait',
  closeTicket: 'wait'
})

const ticketdata = ref({
  id: props.ticketId ?? 0,
  currentStep: currentStep.value,
})

// 显示流转记录
const showTransferRecord = () => {
  if (props.ticketId) {
    showRecordsDialog.value = true
  } else {
    ElMessage.warning('请先保存工单')
  }
}

// 获取步骤状态 - 只在有ticketId时调用
const fetchStepStatus = async () => {
  if (!props.ticketId) return
  
  try {
    const statusRes = await safetyAPI.getStepStatus(props.ticketId)
    stepStatus.value = statusRes
    // 找到当前步骤
    for (const step in stepStatus.value) {
      if (stepStatus.value[step] === 'process') {
        currentStep.value = step
        break
      }
    }
    ticketdata.value.currentStep = currentStep.value
  } catch (error) {
    console.error('获取步骤状态出错:', error)
  }
}



// 步骤样式
const getStepClass = (status: any) => {
  const classMap = {
    wait: 'process-step-wait',
    process: 'process-step-process', 
    finish: 'process-step-finish',
    error: 'process-step-error'
  }
  return classMap[status] || ''
}

// 获取步骤索引
const getStepIndex = computed(() => {
  return Object.keys(stepStatus.value).findIndex(step => 
    stepStatus.value[step] === 'process'
  )
})

// 进入下一步
const nextStep = async (id) => {
  emit('init', id)
  await fetchStepStatus()
 
  
  // 刷新流转记录
  if (showRecordsDialog.value && transferRecordRef.value) {
    transferRecordRef.value.loadData()
  }
}

// 初始化
onMounted(() => {
  fetchStepStatus()
})
</script>

<style scoped>
/* 保持原有样式，移除点击相关样式 */
.process {
  padding: 20px;
  padding-top: 0;
  position: relative;
}

.record-action {
  display: flex;
  justify-content: flex-end;
  margin: 16px 0;
  width: 100%;
}

.step-content {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.step-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 移除点击样式 */
:deep(.el-step) {
  padding: 0 10px;
}

:deep(.el-step__title) {
  font-size: 14px;
  line-height: 1.5;
}

/* 步骤状态样式 */
.process-step-wait {
  --el-step-icon-bg-color: #ccc;
  --el-step-border-color: #ccc;
  --el-step-text-color: #ccc;
}

.process-step-process {
  --el-step-icon-bg-color: #409eff;
  --el-step-border-color: #409eff;
  --el-step-text-color: #409eff;
}

.process-step-finish {
  --el-step-icon-bg-color: #67c23a;
  --el-step-border-color: #67c23a;
  --el-step-text-color: #67c23a;
}

.process-step-error {
  --el-step-icon-bg-color: #f56c6c;
  --el-step-border-color: #f56c6c;
  --el-step-text-color: #f56c6c;
}
</style>
