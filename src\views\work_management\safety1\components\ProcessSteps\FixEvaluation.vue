<template>
  <div class="vulnerability-audit">
    <h2 class="initiate-ticket_title">漏洞评价</h2>

    <!-- 等待界面 -->
    <div v-if="!hasPermission('system:safety:commentsVulns')" class="waiting-audit">
      <el-card class="waiting-card">
        <Wait />
      </el-card>
    </div>
    <!-- 表单 -->
    <el-form v-if="hasPermission('system:safety:commentsVulns')" :model="vulnerabilityForm" label-width="140px">
      <!-- 卡片 -->
      <el-card class="mb-4">
        <template #header>
          <div class="card-header">
            <span>工单创建人信息</span>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :span="6">
             <el-form-item label="工号：" label-width="70px"  prop="employeeId">
              <el-input v-model="vulnerabilityForm.employeeId" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="创建人：" label-width="70px"  prop="applicant">
              <el-input disabled>
                <template v-slot:prepend>
                  <Dictmap code="user0x0" v-model="vulnerabilityForm.applicant" />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="所在单位：" label-width="90px"  prop="deptId">
              <el-input disabled>
                <template v-slot:prepend>
                  <Dictmap code="dept0x0" v-model="vulnerabilityForm.deptId" />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="联系方式：" label-width="90px" prop="mobile">
              <el-input v-model="vulnerabilityForm.mobile" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
       <el-card class="mb-4">
        <template #header>
          <div class="card-header">
            <span>工单基本信息</span>
          </div>
        </template>
        <!-- 第二行 -->
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="工单创建时间：" prop="createTime" disabled>
              <el-date-picker disabled v-model="vulnerabilityForm.createTime" type="datetime" placeholder="选择日期时间"
                style="width: 100%;"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工单结束时间：" prop="deadline" disabled>
              <el-date-picker disabled v-model="vulnerabilityForm.deadline" type="datetime" placeholder="选择日期时间"
                style="width: 100%;"></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        </el-card>
        <el-card class="mb-4" :disabled="showStep">
        <template #header>
          <div class="card-header">
            <span>漏洞整改部门</span>
          </div>
        </template>
          <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="部门名称：">
              <el-input 
                v-model="shstep.deptName"
                disabled
              ></el-input>
        </el-form-item>
          </el-col>
          <el-col :span="8">
             <el-form-item label="联系人：" disabled>
              <el-input 
                v-model="shstep.userName"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
           <el-col :span="8">
             <el-form-item label="联系方式：">
              <el-input 
                v-model="shstep.userMobile"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          </el-row>
        </el-card>
       <!-- 安全工程师信息 -->
      <el-card class="mb-4" :disabled="showStep">
        <template #header>
          <div class="card-header">
          <span>
             安全工程师信息
             <el-tooltip
            placement="top"
            effect="light"
            content="工程师基本信息"
            :raw-content="true"
          >
            <el-icon style="vertical-align: -0.15em" size="16">
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
          </span>
          </div>
        </template>
        <!-- 安全工程师信息开始 -->
        <el-form  :model="vulnerabilityForm" :inline="true" >
        <div>
              <el-form-item label-width="100px" label="工程师名称：" prop="engineerName" style="width:20%">
              <el-input disabled v-model="vulnerabilityForm.engineerName"></el-input>
            </el-form-item>
            <el-form-item label="联系方式：" label-width="90px" prop="engineerMobile" style="width:20%">
              <el-input  disabled v-model="vulnerabilityForm.engineerMobile"> </el-input>
            </el-form-item>
            <el-form-item label="微信号：" label-width="80px" prop="engineerWechat" style="width:20%">
              <el-input disabled v-model="vulnerabilityForm.engineerWechat"></el-input>
            </el-form-item>
            <el-form-item label="QQ号：" label-width="70px" prop="engineerQq" style="width:20%">
              <el-input disabled v-model="vulnerabilityForm.engineerQq"></el-input>
            </el-form-item>
            <el-form-item label="邮箱：" label-width="60px" prop="engineerEmail" style="width:20%">
              <el-input disabled v-model="vulnerabilityForm.engineerEmail"></el-input>
            </el-form-item>
        </div>
        </el-form>
        <!-- 安全工程师信息结束 -->
      </el-card>
        <el-card class="mb-4">
        <template #header>
          <div class="card-header">
            <span>漏洞风险信息</span>
          </div>
        </template>
        <el-row :gutter="24">
            <el-col :span="12">
             <el-form-item label="信息系统名称：" label-width="140px" prop="title">
          <el-input v-model="vulnerabilityForm.title" disabled></el-input>
        </el-form-item>
          </el-col>
           <el-col :span="12">
            <el-form-item label="域名/IP：" label-width="140px" prop="domainIp" maxlength="10">
             <el-input disabled v-model="vulnerabilityForm.domainIp" ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="fontw" label-width="140px" label="漏洞来源：" prop="loopholeSource" disabled>
              <Dictionary v-model="vulnerabilityForm.loopholeSource" code="VLUNS_O"
                disabled></Dictionary>
            </el-form-item>
          </el-col>
          <el-col :span="12">
             <el-form-item  class="fontw" label-width="140px" label="安全漏洞报告：">
                <ul  class="fileList_ul">
                  <li v-for="file in fileList" :key="file.id">
                    <span>{{ file.name }}</span>
                    <el-button type="primary" style="margin-left:20px" @click="downloadFile(file)">下载</el-button>
                  </li>
                  <li v-if="fileList.length<=0">无</li>
                </ul>
              </el-form-item>
          </el-col>
           
           <el-col :span="24">
             <el-form-item  label-width="140px" label="安全漏洞情况描述：" prop="content">
          <el-input type="textarea" v-model="vulnerabilityForm.content" :rows="2" disabled></el-input>
        </el-form-item>
          </el-col>
          
        </el-row>
         <el-form-item class="fontw" label-width="140px" label="安全漏洞详情：" prop="" disabled>
          <el-table :data="vulnerabilityList" style="width: 100%" disabled border>
          <el-table-column align="center" min-width="150" prop="name" label="漏洞名称"></el-table-column>
          <el-table-column align="center" min-width="100" prop="level" label="漏洞级别"></el-table-column>
          <el-table-column align="center" min-width="200" prop="ip" label="服务器IP"></el-table-column>
          <el-table-column align="center" min-width="200" prop="url" label="URL地址"></el-table-column>
          <el-table-column align="center" min-width="200" prop="remark" label="漏洞描述"
            show-overflow-tooltip></el-table-column>
             <el-table-column
            align="center"
             show-overflow-tooltip
            prop="repairSuggestions"
            label="修复建议"
          ></el-table-column>
          <el-table-column align="center" min-width="200" prop="fix" label="是否修复">
            <template #default="scope">
              <el-tag :type="scope.row.fix === 0 ? 'danger' : 'success'">
                {{ scope.row.fix === 1 ? '已修复' : '未修复' }}
              </el-tag>
            </template>
          </el-table-column> <el-table-column align="center" min-width="200" prop="createTime" label="漏洞创建时间"
            show-overflow-tooltip></el-table-column>
          <el-table-column align="center" min-width="200" prop="updateTime" label="漏洞更新时间"
            show-overflow-tooltip></el-table-column>
        </el-table>
        </el-form-item>
      </el-card>
      <!-- 安全漏洞整改反馈 -->
       <el-card class="mb-4" v-if="commentsCorrection">
        <template #header>
          <div class="card-header">
            <span>安全问题整改反馈</span>
          </div>
        </template>
         <el-form-item label="安全问题整改反馈：">
          <el-input disabled type="textarea"   label-width="160px" v-model="commentsCorrection.commentContent" :rows="4" ></el-input>
        </el-form-item>
         <el-col :span="12">
             <el-form-item  class="fontw" label-width="160px" label="漏洞验证报告：">
                <ul>
                  <li v-for="file in commentsCorrection.fileList" :key="file.id">
                    <span>{{ file.name }}</span>
                    <el-button type="primary" style="margin-left:20px" @click="downloadFile(file)">下载</el-button>
                  </li>
                </ul>
              </el-form-item>
           </el-col>
      </el-card>
      <!-- 漏洞评价 -->
      <el-card class="mb-4">
        <template #header>
          <div class="card-header">
            <span>漏洞评价</span>
          </div>
        </template>

        <el-form-item label="整改结果状态：">
          <el-tag :type="vulnerabilityForm.safetyResult==1 ? 'success' : 'danger'" size="large" effect="plain">
            {{ vulnerabilityForm.safetyResult==1 ? '按时完成' : '逾期完成' }}
          </el-tag>
        </el-form-item>

        <el-form-item label="评价备注：">
          <el-input type="textarea" v-model="EvaluationForm.commentContent" :rows="4" :disabled="!showStep"></el-input>
        </el-form-item>

        <!-- 时间 -->
        <el-form-item label="评价时间：">
          <el-date-picker v-model="EvaluationForm.createTime" type="datetime" placeholder="选择日期时间"
            format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" disabled></el-date-picker>
        </el-form-item>

        <el-form-item label="评分：">
          <el-rate v-model="EvaluationForm.rate" clearable :disabled="!showStep" show-score text-color="#ff9900"
            score-template="{value} points" />
        </el-form-item>

        <!-- 表单操作按钮 -->
        <div class="form-actions" v-if="showStep">
          <el-button type="primary" @click="submitAudit">提交评价</el-button>
        </div>
      </el-card>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, defineProps } from 'vue';
import safetyAPI, { safetyPageVO, safetyForm, safetyPageQuery } from "@/api/work_management/safety";
import SafetyManagementAPI, { TicketInfo, StepStatus, StepPermission, VulnerabilityForm } from '@/api/work_management/safety/pocess'
import { ElMessage } from 'element-plus';
import { hasAuth } from "@/plugins/permission";
import Wait from "@/views/error-page/401.vue";
import { formatLocalDateTime } from "@/utils/dateUtils";
interface TicketData {
  id: number;
  currentStep: string;
}

const props = defineProps<{
  ticketdata: TicketData,
}>();
const emit = defineEmits(['next'])
const ID = ref(props.ticketdata ? props.ticketdata.id : null);
const currentStep = ref(props.ticketdata.currentStep);
const nowStep = ref('');
const showStep = ref(false);
const stepStatus = ref<StepStatus>({});
// 表单数据
const vulnerabilityForm = reactive<safetyForm>({
  status: 0,
});
// 整改后文件
const commentsCorrection=ref<Object>({});
// 整改表单
const fixForm = reactive<any>({
  commentType: 1,
  commentContent: '',
  createTime: '',
});
// 复核表单
const VerificationForm = reactive<any>({
  commentType: undefined,
  commentContent: '',
  createTime: '',
});
// 评价表单
const EvaluationForm = reactive<any>({
  commentType: 1,
  commentContent: '',
  createTime: formatLocalDateTime(new Date()),
  rate: 5,
});
// 获取资产类型名称
const getAssetTypeName = (type: number) => {
  const typeMap: Record<number, string> = {
    "-1": "未知类型",
    1: "服务器",
    2: "网络设备",
    3: "安全设备",
    10: "信息系统",
  };
  return typeMap[type] || "未知类型";
};
// 整改部门信息
const shstep=ref({})
// 文件列表
const fileList = ref<Array<any>>([]);
// 整改文件列表
const fixFileList = ref<any[]>([]);
// 复核文件列表
const rechckFileList = ref<any[]>([]);
// 漏洞列表数据
const vulnerabilityList = ref<Array<any>>([]);

function isCurrentStep() {
  if (currentStep.value == nowStep.value) { //是当前步骤
    showStep.value = true;
  } else {
    showStep.value = false;
  }
  console.log("asdasdasdasdasdasd", showStep.value)
}

async function handleQuery() {
  await safetyAPI.getFormData(props.ticketdata.id).then((res) => {
    console.log(res)
    Object.assign(vulnerabilityForm, res);
  });
  console.log(vulnerabilityForm)
  // 整改部门信息
   const foundItem = vulnerabilityForm.reviewProcessForms?.find(item => item.executeDeptType == 3);
   shstep.value=foundItem
  vulnerabilityList.value = vulnerabilityForm.vulns || [];
   fileList.value = vulnerabilityForm.fileList;
  commentsCorrection.value = vulnerabilityForm?.commentsReview ;
  const statusRes: any = await safetyAPI.getStepStatus(props.ticketdata.id);
  stepStatus.value = statusRes;
  for (const step in stepStatus.value) {
    if (stepStatus.value[step as keyof StepStatus] == 'process') {
      nowStep.value = step as keyof StepStatus;
      break;
    }
  }
  // await safetyAPI.getFileList(ID.value).then((data) => {
  //   fileList.value = data;
  // })
  let resVFix: any[] = [];
  await safetyAPI.getVulns(props.ticketdata.id).then((res: any) => {
    const resFix = res.filter((item: any) => item.step == 3);
    resVFix = res.filter((item: any) => item.step == 4);
    Object.assign(fixForm, resFix[resFix.length - 1]);
    fixFileList.value = fixForm.fileList || [];
  });
  let filteredComments = vulnerabilityForm.comments ? vulnerabilityForm.comments.filter((item: any) => item.step == 4) : [];
  if (filteredComments.length > 0) {
    Object.assign(VerificationForm, filteredComments[filteredComments.length - 1]);
    rechckFileList.value = resVFix[resVFix.length - 1].fileList || [];
  }
  isCurrentStep();
  if (!showStep.value) {
    let filteredComments = vulnerabilityForm.comments ? vulnerabilityForm.comments.filter((item: any) => item.step == 5) : [];
    if (filteredComments.length > 0) {
      Object.assign(EvaluationForm, filteredComments[filteredComments.length - 1]);
    }
  }
}
// 附件下载
const downloadFile = (row: any) => {
  console.log(row)
  // const fileUrl = row.fileList && row.fileList[0] ? row.fileList[0].url : null; // 添加 null check
  if (row.url) {
  fetch(row.url)
  .then(response => response.blob())
  .then(blob => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = row.name; // 设置自定义文件名
    link.style.display = 'none'; // 隐藏链接
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  })
  .catch(error => {
      ElMessage.error('附件不存在');
  });
    // console.log( row.url,row.name)
    // // window.open(fileUrl, '_blank');
    //   const link = document.createElement("a");
    //   link.href = row.url;
    //   link.download = row.name;
    //   link.style.display = 'none'; // 隐藏链接
    //   document.body.appendChild(link);
    //   link.click();
    //   document.body.removeChild(link);
  } else {
    ElMessage.error('附件不存在');
  }
}


// 提交
const submitAudit = async () => {
  await ElMessageBox.confirm(
    '确定要提交漏洞评价吗？',
    '确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  );
  try {
    await safetyAPI.evaluateVulns(props.ticketdata.id, EvaluationForm);
    // 显示成功消息
    ElMessage.success('评价已提交');

    emit('next');
  } catch (error) {
    console.error('Error submitting audit:', error);
    ElMessage.error('提交失败，请重试');
  }
}

// 权限判断
function hasPermission(requiredPerms: string): boolean {
  console.log(hasAuth(requiredPerms, 'button'));
  return hasAuth(requiredPerms, 'button');
}
onMounted(() => {
  handleQuery();
});
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-actions {
  margin-top: 20px;
  text-align: center;
}
.initiate-ticket_title {
  margin-top: 0;
}
.base_title {
  font-weight: bold;
  margin: 0;
  padding: 0;
  height: 64px;
  line-height: 64px;
  font-size: 16px !important;
}
:deep(.el-form-item) {
  margin-right: 0;
}
.fileList_ul{
  width: 100%;
}
 .fileList_ul li{
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f7f6f6;
  padding: 5px;
  border-radius: 4px;
  margin-bottom: 10px;
  }
</style>
