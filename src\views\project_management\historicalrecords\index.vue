<template>
  <div v-loading="pageLoading">
    <!-- 任务名称关键词搜索框及说明 -->
    <div class="search-wrapper">
      <span class="input-label">任务关键词搜索：</span>
      <el-select
        v-model="currentProjectId"
        filterable
        placeholder="选择项目"
        style="position: relative; width: 200px; margin-bottom: 10px"
        @change="handleSwitchProject"
      >
        <el-option
          v-for="proj in projectList"
          :key="proj.id"
          :label="proj.name"
          :value="proj.id"
        />
      </el-select>
    </div>

    <el-table
      :data="treeTableData"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      class="gantt-el-table"
      :expand-row-keys="expandedRowKeys"
      @expand-change="handleExpandChange"
      :row-class-name="getRowClass"
      :fit="true"
      :header-cell-style="headerCellStyle"
      :cell-style="cellStyle"
    >
      <el-table-column type="selection" width="40" />
      <el-table-column
        prop="title"
        label="标题"
        min-width="200px"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <!-- 根据type字段显示对应图标 -->
          <el-icon
            v-if="scope.row.type === 'milestone'"
            style="margin-right: 6px; color: #f56c6c"
          >
            <Flag />
          </el-icon>
          <el-icon
            v-else-if="scope.row.type === 'taskFolder'"
            style="margin-right: 6px; color: #409eff"
          >
            <Folder />
          </el-icon>
          <el-icon v-else style="margin-right: 6px; color: #67c23a">
            <Document />
          </el-icon>
          <span
            :class="{
              'milestone-title': scope.row.type === 'milestone',
              'folder-title': scope.row.type === 'taskFolder',
              'task-title': scope.row.type === 'task',
            }"
          >
            {{ scope.row.title }}
          </span>
        </template>
      </el-table-column>
      <!-- 直接使用接口返回的status字段 -->
      <el-table-column prop="status" label="状态" width="90">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.status)">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="priority" label="优先级" width="90">
        <template #default="{ row }">
          <el-tag :type="getPriorityTagType(row.priority)">
            {{ row.priority }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="250">
        <template #default="scope">
          <!-- 新建子项按钮 - 只有里程碑和任务文件夹可以创建子项 -->
          <el-button
            v-if="scope.row.type !== 'task'"
            link
            type="success"
            size="small"
            style="padding: 0 4px; min-width: unset"
            @click="openCreateDialog(scope.row)"
          >
            新建
          </el-button>
          <!-- 移动按钮 - 里程碑不能移动 -->
          <el-button
            v-if="scope.row.type !== 'milestone'"
            link
            type="info"
            size="small"
            style="padding: 0 4px; min-width: unset"
            @click="openMoveDialog(scope.row)"
          >
            移动
          </el-button>
          <el-button
            link
            type="primary"
            size="small"
            style="padding: 0 4px; min-width: unset"
            @click="openViewDialog(scope.row)"
          >
            查看
          </el-button>
          <el-button
            link
            type="warning"
            size="small"
            style="padding: 0 4px; min-width: unset"
            @click="openEditDialog(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            size="small"
            style="padding: 0 4px; min-width: unset"
            @click="openDeleteDialog(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>

  <!-- 对话框部分 -->
  <el-dialog v-model="createDialogVisible" title="新建工作项" width="400px">
    <el-form :model="createForm" label-width="80px">
      <el-form-item label="标题" required>
        <el-input v-model="createForm.title" />
      </el-form-item>
      <el-form-item label="类型" required>
        <el-select v-model="createForm.type" @change="handleTypeChange">
          <el-option
            v-for="option in availableCreateTypes"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务分配" required>
        <el-tree-select
          v-model="createForm.parentId"
          :data="milestoneTreeData"
          :props="{ label: 'title', children: 'children', value: 'id' }"
          placeholder="选择所属里程碑/任务文件夹"
          clearable
          check-strictly
          style="width: 100%"
          :disabled="createForm.type === 'milestone'"
          @change="handleParentChange"
        />
      </el-form-item>
      <el-form-item label="人员分配" required>
        <el-select
          v-model="createForm.members"
          multiple
          filterable
          placeholder="请选择成员"
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.username"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="优先级" required>
        <el-select v-model="createForm.priority">
          <el-option label="高" value="高" />
          <el-option label="普通" value="普通" />
          <el-option label="低" value="低" />
        </el-select>
      </el-form-item>
      <el-form-item label="开始日期" required>
        <el-date-picker
          v-model="createForm.start"
          type="date"
          value-format="YYYY-MM-DD"
          @change="validateDateRange('create')"
        />
      </el-form-item>
      <el-form-item label="截止日期" required :error="createDateError">
        <el-date-picker
          v-model="createForm.end"
          type="date"
          value-format="YYYY-MM-DD"
          @change="
            () => {
              validateDateRange('create');
              autoUpdateStatusByDate(createForm);
            }
          "
        />
      </el-form-item>
      <!-- 新增任务描述字段 -->
      <el-form-item label="任务描述">
        <el-input
          v-model="createForm.description"
          type="textarea"
          :rows="4"
          placeholder="请输入任务描述信息"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="createDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleCreate">确定</el-button>
    </template>
  </el-dialog>

  <el-dialog v-model="editDialogVisible" title="编辑工作项" width="400px">
    <el-form :model="editForm" label-width="80px">
      <el-form-item label="标题" required>
        <el-input v-model="editForm.title" />
      </el-form-item>
      <el-form-item label="类型" required>
        <el-select v-model="editForm.type" @change="handleEditTypeChange">
          <el-option
            v-for="option in availableEditTypes"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务分配" required>
        <el-tree-select
          v-model="editForm.parentId"
          :data="milestoneTreeData"
          :props="{ label: 'title', children: 'children', value: 'id' }"
          placeholder="选择所属里程碑/任务文件夹"
          clearable
          check-strictly
          style="width: 100%"
          :disabled="editForm.type === 'milestone'"
          @change="handleEditParentChange"
        />
      </el-form-item>
      <el-form-item label="人员分配" required>
        <el-select
          v-model="editForm.members"
          multiple
          filterable
          placeholder="请选择成员"
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.username"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="优先级" required>
        <el-select v-model="editForm.priority">
          <el-option label="高" value="高" />
          <el-option label="普通" value="普通" />
          <el-option label="低" value="低" />
        </el-select>
      </el-form-item>
      <el-form-item label="开始日期" required>
        <el-date-picker
          v-model="editForm.start"
          type="date"
          value-format="YYYY-MM-DD"
          @change="validateDateRange('edit')"
        />
      </el-form-item>
      <el-form-item label="截止日期" required :error="editDateError">
        <el-date-picker
          v-model="editForm.end"
          type="date"
          value-format="YYYY-MM-DD"
          @change="
            () => {
              validateDateRange('edit');
              autoUpdateStatusByDate(editForm);
            }
          "
        />
      </el-form-item>
      <!-- 编辑对话框也添加任务描述 -->
      <el-form-item label="任务描述">
        <el-input
          v-model="editForm.description"
          type="textarea"
          :rows="4"
          placeholder="请输入任务描述信息"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="editDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleEdit">保存</el-button>
    </template>
  </el-dialog>

  <el-dialog v-model="viewDialogVisible" title="工作项详情" width="400px">
    <el-descriptions :column="1" border>
      <el-descriptions-item label="标题">
        {{ viewRow.title }}
      </el-descriptions-item>
      <el-descriptions-item label="类型">
        {{ formatTypeLabel(viewRow.type) }}
      </el-descriptions-item>
      <el-descriptions-item label="优先级">
        {{ viewRow.priority }}
      </el-descriptions-item>
      <!-- 直接使用接口返回的status字段 -->
      <el-descriptions-item label="状态">
        {{ viewRow.status }}
      </el-descriptions-item>
      <el-descriptions-item label="开始日期">
        {{ formatIsoToDisplay(viewRow.start) }}
      </el-descriptions-item>
      <el-descriptions-item label="截止日期">
        {{ formatIsoToDisplay(viewRow.end) }}
      </el-descriptions-item>
      <el-descriptions-item label="完成时间">
        {{ viewRow.finish ? formatIsoToDisplay(viewRow.finish) : "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="人员分配">
        {{
          viewRow.members && viewRow.members.length
            ? viewRow.members
                .map(
                  (id) =>
                    userList.find((u) => u.id === id)?.username || "未知用户"
                )
                .join(", ")
            : "-"
        }}
      </el-descriptions-item>
      <!-- 查看对话框添加任务描述 -->
      <el-descriptions-item label="任务描述">
        {{ viewRow.description || "-" }}
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <el-button @click="viewDialogVisible = false">关闭</el-button>
    </template>
  </el-dialog>

  <el-dialog v-model="deleteDialogVisible" title="确认删除" width="340px">
    <div style="margin: 20px 0">
      确定要删除该工作项吗？
      <template v-if="deleteRow && hasChildren(deleteRow)">
        <br />
        该工作项包含子项，删除后所有子项也将被一并删除。
      </template>
    </div>
    <template #footer>
      <el-button @click="deleteDialogVisible = false">取消</el-button>
      <el-button type="danger" @click="handleDelete">删除</el-button>
    </template>
  </el-dialog>

  <!-- 移动对话框 -->
  <el-dialog v-model="moveDialogVisible" title="移动工作项" width="400px">
    <el-form :model="moveForm" label-width="80px">
      <el-form-item label="工作项">
        <el-input :value="moveForm.taskTitle" disabled />
      </el-form-item>
      <el-form-item label="类型">
        <el-input :value="formatTypeLabel(moveForm.taskType)" disabled />
      </el-form-item>
      <el-form-item label="移动到" required>
        <el-tree-select
          v-model="moveForm.newParentId"
          :data="availableMoveTargets"
          :props="{ label: 'title', children: 'children', value: 'id' }"
          placeholder="选择 移动目标 位置"
          clearable
          check-strictly
          style="width: 100%"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="moveDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleMove">确定移动</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { Document, Folder, Flag } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import {
  TaskAPI,
  ProjectAPI,
  UserAPI,
} from "@/api/progress_management/createProject";
import {
  TaskRow,
  Project,
  User,
  TaskType,
  TaskPriority,
  TaskStatus,
} from "@/types/project";

// 状态
// 状态_页面加载
const pageLoading = ref(false);

// 扩展TaskRow接口，添加description字段
interface TaskRowWithDescription extends TaskRow {
  description?: string;
}

// 格式化日期工具函数
const formatToIsoTime = (dateString: string) => {
  if (!dateString) return "";
  return dayjs(dateString).toISOString();
};

const formatIsoToDisplay = (isoString: string) => {
  if (!isoString) return "";
  return dayjs(isoString).format("YYYY-MM-DD");
};

// 判断日期是否逾期（截止日期 < 当前时间）
const isOverdue = (endDate: string) => {
  return dayjs(endDate).isBefore(dayjs(), "day");
};

// 根据日期自动更新状态
const autoUpdateStatusByDate = (form: TaskRowWithDescription) => {
  // 仅处理当前状态为“已逾期”的任务
  if (form.status !== "已逾期") return;

  // 若修改后的截止日期 >= 当前时间，自动更新状态为“未开始”
  if (!isOverdue(form.end)) {
    form.status = "未开始";
    ElMessage.info('任务已不再逾期，状态已更新为"未开始"');
  }
};

// 数据存储
const treeTableData = ref<TaskRowWithDescription[]>([]);
const flatData = ref<TaskRowWithDescription[]>([]);
const projectList = ref<Project[]>([]);
const userList = ref<User[]>([]);
const currentProjectId = ref<number | null>(null);

// 路由实例
const route = useRoute();
const router = useRouter();

// 用于判断和验证子项与里程碑时间关系的变量
const parentMilestone = ref<TaskRowWithDescription | null>(null);
const editParentMilestone = ref<TaskRowWithDescription | null>(null);
const createDateError = ref("");
const editDateError = ref("");

// 里程碑树形数据构建逻辑
const milestoneTreeData = computed(() => {
  // 创建原始数据的深拷贝，避免修改源数据
  const treeCopy = JSON.parse(
    JSON.stringify(treeTableData.value)
  ) as TaskRowWithDescription[];

  // 构建树形结构的递归函数
  const buildTree = (items: TaskRowWithDescription[]): any[] => {
    return (
      items
        // 只过滤掉当前正在编辑的项，避免自引用
        .filter((item) => item.id !== editForm.value.id && item.id != null)
        .map((item) => {
          // 递归处理子项
          const children =
            item.children && item.children.length
              ? buildTree(item.children as TaskRowWithDescription[])
              : [];

          return {
            id: item.id,
            title: item.title,
            children: children,
          };
        })
    );
  };

  // 构建完整树形结构，包含所有可用的父项选择
  const filteredTree = buildTree(treeCopy);

  // 返回包含顶级选项的树形数据
  return [
    {
      id: -1,
      title: "顶级任务",
      children: filteredTree,
    },
  ];
});

// 构建树形结构
function buildTree(
  flatData: TaskRowWithDescription[]
): TaskRowWithDescription[] {
  const map = new Map<number, TaskRowWithDescription>();
  const tree: TaskRowWithDescription[] = [];

  // 首先创建所有节点的映射
  flatData.forEach((item) => {
    if (item.id != null) {
      // 强制创建新对象，确保响应式
      map.set(item.id, { ...item, children: [...(item.children || [])] });
    }
  });

  // 构建树形结构
  flatData.forEach((item) => {
    if (item.id == null) return;
    const currentItem = map.get(item.id)!;
    if (item.parentId === -1 || item.parentId === null) {
      tree.push(currentItem);
    } else {
      const parent = item.parentId ? map.get(item.parentId) : undefined;
      if (parent) {
        parent.children = [...(parent.children || []), currentItem];
      } else {
        console.warn("buildTree: 找不到 parentId =", item.parentId, "的父节点");
        tree.push(currentItem);
      }
    }
  });

  return tree;
}

// 更新父任务时间
function updateParentTaskTime(
  nodes: TaskRowWithDescription[]
): TaskRowWithDescription[] {
  const nodesCopy = JSON.parse(
    JSON.stringify(nodes)
  ) as TaskRowWithDescription[];
  function processNodes(list: TaskRowWithDescription[], depth = 0) {
    if (depth > 100) return; // 防止递归过深
    for (const node of list) {
      if (node.type === "milestone") continue;

      if (node.children && node.children.length > 0) {
        processNodes(node.children as TaskRowWithDescription[], depth + 1);
        let minStart: string | null = null;
        let maxEnd: string | null = null;

        function collectTimes(items: TaskRowWithDescription[]) {
          for (const item of items) {
            if (item.children && item.children.length > 0) {
              collectTimes(item.children as TaskRowWithDescription[]);
            }
            if (item.start && dayjs(item.start).isValid()) {
              if (!minStart || dayjs(item.start).isBefore(minStart)) {
                minStart = item.start;
              }
            }
            if (item.end && dayjs(item.end).isValid()) {
              if (!maxEnd || dayjs(item.end).isAfter(maxEnd)) {
                maxEnd = item.end;
              }
            }
          }
        }
        collectTimes(node.children as TaskRowWithDescription[]);
        if (minStart) node.start = minStart;
        if (maxEnd) node.end = maxEnd;
      }
    }
  }
  processNodes(nodesCopy);
  return nodesCopy;
}

// 获取所有任务（递归遍历所有层级）
function getAllTasksRecursive(
  nodes: TaskRowWithDescription[]
): TaskRowWithDescription[] {
  let allTasks: TaskRowWithDescription[] = [];
  nodes.forEach((node) => {
    allTasks.push(node);
    if (node.children && node.children.length > 0) {
      allTasks = allTasks.concat(
        getAllTasksRecursive(node.children as TaskRowWithDescription[])
      );
    }
  });
  return allTasks;
}

// 查找祖先里程碑
function findAncestorMilestone(
  taskId: number | null | undefined,
  allTasks: TaskRowWithDescription[]
): TaskRowWithDescription | null {
  if (taskId == null) return null;

  const task = allTasks.find((t) => t.id === taskId);
  if (!task) return null;

  // 如果当前任务就是里程碑，返回自身
  if (task.type === "milestone") return task;

  // 如果没有父节点，返回null
  if (task.parentId === null || task.parentId === -1) return null;

  // 递归查找父节点
  return findAncestorMilestone(task.parentId, allTasks);
}

// 递归获取所有子项ID（包括当前项）
function getAllChildIds(
  item: TaskRowWithDescription,
  allItems: TaskRowWithDescription[]
): number[] {
  const ids: number[] = [];
  if (item.id != null) {
    ids.push(item.id);
  }

  // 查找当前项的直接子项
  const children = allItems.filter((child) => child.parentId === item.id);

  // 递归收集子项的子项
  children.forEach((child) => {
    ids.push(...getAllChildIds(child, allItems));
  });

  return ids;
}

// 判断是否有子项
function hasChildren(row: TaskRowWithDescription): boolean {
  const allItems = getAllTasksRecursive(treeTableData.value);
  return allItems.some((item) => item.parentId === row.id);
}

// 获取数据
async function fetchData(projectId: number) {
  try {
    if (!projectId) {
      console.error("fetchData: 无效的 projectId:", projectId);
      ElMessage.error("无效的项目ID");
      flatData.value = [];
      treeTableData.value = [];
      return;
    }

    // 1. 强制清空旧数据
    flatData.value = [];
    treeTableData.value = [];
    await nextTick();

    // 2. 重新拉取数据
    const tasksResponse = await TaskAPI.getList(projectId);

    // 标准化数据格式
    const tasks = Array.isArray(tasksResponse)
      ? tasksResponse
      : tasksResponse?.data && Array.isArray(tasksResponse.data)
        ? tasksResponse.data
        : [tasksResponse];

    if (!Array.isArray(tasks) || !tasks.length) {
      console.error("fetchData: 暂无数据:", tasksResponse);
      ElMessage.warning("暂无数据");
      return;
    }

    // 3. 格式化数据，包含description字段
    const formattedTasks = tasks
      .filter((item) => item.id != null)
      .map((item: any) => ({
        ...item,
        id: Number(item.id),
        parentId:
          item.parentId === null || item.parentId === undefined
            ? -1
            : Number(item.parentId),
        members: Array.isArray(item.members) ? item.members.map(Number) : [],
        type: item.type || "task",
        start: item.start
          ? formatIsoToDisplay(item.start)
          : dayjs().format("YYYY-MM-DD"),
        end: item.end
          ? formatIsoToDisplay(item.end)
          : dayjs().add(1, "day").format("YYYY-MM-DD"),
        finish: item.finish ? formatIsoToDisplay(item.finish) : null,
        children: item.children || [],
        description: item.description || "", // 处理描述字段
      }));

    // 4. 强制替换数据，触发响应式更新
    flatData.value = [...formattedTasks];
    const tree = buildTree(flatData.value);
    treeTableData.value = updateParentTaskTime(tree);
debugger
    // 5. 等待DOM更新
    await nextTick();
  } catch (error) {
    console.error("fetchData: 错误:", error);
    ElMessage.error("获取任务数据失败");
    flatData.value = [];
    treeTableData.value = [];
  }
}

// 独立的获取项目列表方法
async function fetchProjectList() {
  try {
    const projectsResponse = await ProjectAPI.getList();
    const projects = Array.isArray(projectsResponse)
      ? projectsResponse
      : projectsResponse?.data && Array.isArray(projectsResponse.data)
        ? projectsResponse.data
        : [];

    projectList.value = Array.isArray(projects)
      ? projects
          .filter((item) => item.id != null)
          .map((item: Project) => ({ ...item, id: Number(item.id) }))
      : [];
  } catch (error) {
    console.error("获取项目列表失败:", error);
    ElMessage.error("获取项目列表失败");
  }
}

// 初始化数据
async function initData() {
  pageLoading.value = true;
  try {
    // 并行获取项目列表和用户列表
    await Promise.all([
      fetchProjectList(), // 使用独立的项目列表获取方法
      (async () => {
        const usersResponse = await UserAPI.getList();
        // 处理用户数据
        const users = Array.isArray(usersResponse)
          ? usersResponse
          : usersResponse?.data && Array.isArray(usersResponse.data)
            ? usersResponse.data
            : [];
        userList.value = Array.isArray(users)
          ? users
              .filter((item) => item.id != null && !isNaN(Number(item.id)))
              .map((item: User) => ({
                ...item,
                id: Number(item.id),
                name: item.username || "未知用户",
              }))
          : [];
      })(),
    ]);

    // 处理项目ID
    const projectIdFromRoute = route.params.projectId
      ? Number(route.params.projectId)
      : null;

    if (
      projectIdFromRoute &&
      projectList.value.some((p) => p.id === projectIdFromRoute)
    ) {
      currentProjectId.value = projectIdFromRoute;
    } else if (projectList.value.length > 0) {
      currentProjectId.value = projectList.value[0].id;
    }

    if (currentProjectId.value) {
      await fetchData(currentProjectId.value);
    } else {
      console.error("initData: 无有效的 projectId");
      ElMessage.error("无可用项目，请先创建项目");
      router.push({ name: "DefaultRoute" });
    }
  } catch (error) {
    console.error("initData: 错误:", error);
    ElMessage.error("初始化数据失败");
  } finally {
    pageLoading.value = false;
  }
}

// 切换项目
async function handleSwitchProject(id: number) {
  currentProjectId.value = id;
  expandedRowKeys.value = [];
  await fetchData(id);
}

// 展开行处理
const expandedRowKeys = ref<string[]>([]);
function handleExpandChange(row: TaskRowWithDescription, expanded: boolean) {
  const id = String(row.id);
  if (expanded && row.id != null) {
    if (!expandedRowKeys.value.includes(id)) {
      expandedRowKeys.value.push(id);
    }
  } else {
    expandedRowKeys.value = expandedRowKeys.value.filter((k) => k !== id);
  }
}

// 获取状态标签类型
function getStatusTagType(status: string) {
  if (status === "已完成") return "success";
  if (status === "进行中") return "warning";
  if (status === "未开始") return "info";
  if (status === "已逾期") return "danger";
  return "info";
}

// 获取优先级标签类型
function getPriorityTagType(priority: string) {
  if (priority === "高") return "danger";
  if (priority === "普通") return "primary";
  if (priority === "低") return "info";
  return "info";
}

// 获取行样式类
function getRowClass({ row }: { row: TaskRowWithDescription }) {
  return row.children && row.children.length > 0 ? "gantt-folder-row" : "";
}

// 表格样式
const headerCellStyle = {
  padding: "8px 0",
  background: "#fafbfc",
  color: "#888",
  fontWeight: "bold",
  border: "1px solid #ebeef5",
};

const cellStyle = { padding: "8px 0", border: "1px solid #ebeef5" };

// 对话框状态
const createDialogVisible = ref(false);
const createForm = ref<TaskRowWithDescription>({
  id: 0,
  title: "",
  type: "milestone" as TaskType,
  priority: "普通" as TaskPriority,
  status: "未开始" as TaskStatus, // 默认状态
  start: dayjs().format("YYYY-MM-DD"),
  end: dayjs().add(1, "day").format("YYYY-MM-DD"),
  parentId: -1,
  members: [],
  children: [],
  projectId: 0,
  owner: "",
  canUpload: false,
  description: "", // 新增描述字段
});

// 计算可选的类型选项
const availableCreateTypes = computed(() => {
  const parentId = createForm.value.parentId;
  let parentType: string | null = null;

  if (parentId !== -1 && parentId !== null) {
    const allTasks = getAllTasksRecursive(treeTableData.value);
    const parentTask = allTasks.find((task) => task.id === parentId);
    parentType = parentTask?.type || null;
  }

  const allowedTypes = getAllowedChildTypes(parentType);

  const typeOptions = [
    { label: "里程碑", value: "milestone" },
    { label: "任务文件夹", value: "taskFolder" },
    { label: "任务", value: "task" },
  ];

  return typeOptions.filter((option) => allowedTypes.includes(option.value));
});

// 计算可选的编辑类型选项
const availableEditTypes = computed(() => {
  const parentId = editForm.value.parentId;
  let parentType: string | null = null;

  if (parentId !== -1 && parentId !== null) {
    const allTasks = getAllTasksRecursive(treeTableData.value);
    const parentTask = allTasks.find((task) => task.id === parentId);
    parentType = parentTask?.type || null;
  }

  const allowedTypes = getAllowedChildTypes(parentType);

  const typeOptions = [
    { label: "里程碑", value: "milestone" },
    { label: "任务文件夹", value: "taskFolder" },
    { label: "任务", value: "task" },
  ];

  return typeOptions.filter((option) => allowedTypes.includes(option.value));
});

const viewDialogVisible = ref(false);
const viewRow = ref<TaskRowWithDescription>({} as TaskRowWithDescription);
const deleteDialogVisible = ref(false);
const deleteRow = ref<TaskRowWithDescription | null>(null);
const editDialogVisible = ref(false);
const editForm = ref<TaskRowWithDescription>({
  id: 0,
  title: "",
  type: "task" as TaskType,
  priority: "普通" as TaskPriority,
  status: "未开始" as TaskStatus,
  start: "",
  end: "",
  parentId: -1,
  members: [],
  children: [],
  projectId: 0,
  owner: "",
  canUpload: false,
  description: "", // 新增描述字段
});

// 移动功能相关状态
const moveDialogVisible = ref(false);
const moveForm = ref();

// 计算可移动到的目标节点列表
const availableMoveTargets = computed(() => {
  const taskType = moveForm.value.taskType;
  const taskId = moveForm.value.taskId;

  // 获取当前要移动的任务
  const allTasks = getAllTasksRecursive(treeTableData.value);
  const currentTask = allTasks.find((task) => task.id === taskId);
  if (!currentTask) {
    ("找不到当前任务");
    return [];
  }

  // 查找当前任务所属的里程碑
  const currentMilestone = findAncestorMilestone(taskId, allTasks);
  if (!currentMilestone) {
    ("没有找到里程碑");
    return [];
  }

  // 递归获取所有子项ID（包括当前项）用于排除
  const getAllChildIds = (item: TaskRowWithDescription): number[] => {
    const ids: number[] = [];
    if (item.id != null) {
      ids.push(item.id);
    }
    // 查找当前项的直接子项
    const children = allTasks.filter((child) => child.parentId === item.id);
    // 递归收集子项的子项
    children.forEach((child) => {
      ids.push(...getAllChildIds(child));
    });
    return ids;
  };

  const excludeIds = getAllChildIds(currentTask);

  // 构建树形结构的递归函数
  const buildTargetTree = (parentId: number | null): any[] => {
    const candidates = allTasks.filter((task) => {
      // 检查父ID匹配 - 需要处理 -1 和 null 的情况
      if (parentId === null) {
        // 查找顶级项：parentId 为 null 或 -1
        return task.parentId === null || task.parentId === -1;
      } else {
        // 查找特定父ID的子项
        return task.parentId === parentId;
      }
    });

    return candidates
      .filter((task) => {
        `检查任务 ${task.title} (ID: ${task.id}, type: ${task.type}, parentId: ${task.parentId})`;

        // 1. 不是要移动的任务及其子项
        if (excludeIds.includes(task.id!)) {
          ("  - 被排除（是移动项或其子项）");
          return false;
        }

        // 2. 必须在当前里程碑下
        const taskMilestone = findAncestorMilestone(task.id!, allTasks);
        if (task.type === "milestone") {
          // 如果是里程碑，必须是当前里程碑
          if (task.id !== currentMilestone.id) {
            `  - 被排除（不是当前里程碑，当前里程碑ID: ${currentMilestone.id}）`;
            return false;
          }
        } else {
          // 如果不是里程碑，必须属于当前里程碑
          if (!taskMilestone || taskMilestone.id !== currentMilestone.id) {
            `  - 被排除（不属于当前里程碑，taskMilestone=${taskMilestone?.title}，currentMilestone=${currentMilestone.title}）`;
            return false;
          }
        }

        // 3. 根据移动项的类型判断是否可以作为目标
        if (taskType === "taskFolder") {
          // 任务文件夹可以移动到里程碑下或另一个任务文件夹下
          const valid = task.type === "milestone" || task.type === "taskFolder";
          if (!valid)
            "  - 被排除（类型不匹配：任务文件夹只能移到里程碑或任务文件夹）";
          else "  - 通过所有检查，将被包含";
          return valid;
        } else if (taskType === "task") {
          // 任务可以移动到里程碑下或任务文件夹下
          const valid = task.type === "milestone" || task.type === "taskFolder";
          if (!valid)
            "  - 被排除（类型不匹配：任务只能移到里程碑或任务文件夹）";
          else "  - 通过所有检查，将被包含";
          return valid;
        }

        ("  - 被排除（未知原因）");
        return false;
      })
      .map((task) => {
        `  - 包含任务：${task.title}`;
        return {
          id: task.id,
          title: task.title,
          type: task.type,
          children: buildTargetTree(task.id!),
        };
      });
  };

  // 从顶级开始构建树形结构
  const result = buildTargetTree(null);
  return result;
});

// 格式化类型标签
function formatTypeLabel(type: string): string {
  switch (type) {
    case "milestone":
      return "里程碑";
    case "task":
      return "任务";
    case "taskFolder":
      return "任务文件夹";
    default:
      return type || "未知类型";
  }
}

// 类型变更处理
function handleTypeChange() {
  if (createForm.value.type === "milestone") {
    createForm.value.parentId = -1;
    parentMilestone.value = null;
  } else {
    handleParentChange(createForm.value.parentId);
  }
}

// 父项变更处理
function handleParentChange(parentId: number | null | undefined) {
  const actualParentId = parentId ?? -1;
  const allTasks = getAllTasksRecursive(treeTableData.value);
  parentMilestone.value =
    actualParentId === -1
      ? null
      : findAncestorMilestone(actualParentId, allTasks);

  // 如果是里程碑的子项，自动设置时间为里程碑的时间
  if (parentMilestone.value) {
    createForm.value.start =
      parentMilestone.value.start || dayjs().format("YYYY-MM-DD");
    createForm.value.end =
      parentMilestone.value.end || dayjs().add(1, "day").format("YYYY-MM-DD");
  }

  validateDateRange("create");
}

// 编辑类型变更处理
function handleEditTypeChange() {
  if (editForm.value.type === "milestone") {
    editForm.value.parentId = -1;
    editParentMilestone.value = null;
  } else {
    handleEditParentChange(editForm.value.parentId);
  }
}

// 编辑父项变更处理
function handleEditParentChange(parentId: number | null | undefined) {
  const actualParentId = parentId ?? -1;
  const allTasks = getAllTasksRecursive(treeTableData.value);
  editParentMilestone.value =
    actualParentId === -1
      ? null
      : findAncestorMilestone(actualParentId, allTasks);

  // 如果是里程碑的子项，自动设置时间为里程碑的时间
  if (editParentMilestone.value) {
    editForm.value.start =
      editParentMilestone.value.start || dayjs().format("YYYY-MM-DD");
    editForm.value.end =
      editParentMilestone.value.end ||
      dayjs().add(1, "day").format("YYYY-MM-DD");
  }

  validateDateRange("edit");
}

// 层级约束验证函数
function validateHierarchy(
  type: string,
  parentId: number | null
): { valid: boolean; message: string } {
  // 顶层只能是里程碑
  if (parentId === null || parentId === -1) {
    if (type !== "milestone") {
      return {
        valid: false,
        message: "顶层只能创建里程碑",
      };
    }
    return { valid: true, message: "" };
  }

  // 查找父节点
  const allTasks = getAllTasksRecursive(treeTableData.value);
  const parentTask = allTasks.find((task) => task.id === parentId);

  if (!parentTask) {
    return {
      valid: false,
      message: "找不到父节点",
    };
  }

  // 根据父节点类型验证子节点类型
  switch (parentTask.type) {
    case "milestone":
      // 里程碑下只能有任务文件夹和任务
      if (type !== "taskFolder" && type !== "task") {
        return {
          valid: false,
          message: "里程碑下只能创建任务文件夹或任务",
        };
      }
      break;

    case "taskFolder":
      // 任务文件夹下只能有任务文件夹和任务
      if (type !== "taskFolder" && type !== "task") {
        return {
          valid: false,
          message: "任务文件夹下只能创建任务文件夹或任务",
        };
      }
      break;

    case "task":
      // 任务下不能有子项
      return {
        valid: false,
        message: "任务下不能创建子项",
      };

    default:
      return {
        valid: false,
        message: "未知的父节点类型",
      };
  }

  return { valid: true, message: "" };
}

// 获取允许的子项类型
function getAllowedChildTypes(parentType: string | null): string[] {
  if (parentType === null) {
    // 顶层只能创建里程碑
    return ["milestone"];
  }

  switch (parentType) {
    case "milestone":
      return ["taskFolder", "task"];
    case "taskFolder":
      return ["taskFolder", "task"];
    case "task":
      return []; // 任务下不能有子项
    default:
      return [];
  }
}

// 验证日期范围是否在里程碑范围内
function validateDateRange(formType: "create" | "edit") {
  const form = formType === "create" ? createForm.value : editForm.value;
  const milestone =
    formType === "create" ? parentMilestone.value : editParentMilestone.value;
  const errorRef = formType === "create" ? createDateError : editDateError;

  errorRef.value = "";

  if (!milestone) return true;

  const start = dayjs(form.start);
  const end = dayjs(form.end);
  const milestoneStart = dayjs(milestone.start);
  const milestoneEnd = dayjs(milestone.end);

  if (!start.isValid() || !end.isValid()) {
    errorRef.value = "请选择有效的日期";
    return false;
  }

  if (
    start.isBefore(milestoneStart, "day") ||
    end.isAfter(milestoneEnd, "day")
  ) {
    errorRef.value = `子项时间不能超过里程碑时间范围`;
    return false;
  }
  return true;
}

// 打开创建对话框
function openCreateDialog(parentRow?: TaskRowWithDescription) {
  const allowedTypes = getAllowedChildTypes(parentRow?.type || null);
  const defaultType = (allowedTypes[0] || "milestone") as TaskType;

  createForm.value = {
    id: 0,
    title: "",
    type: defaultType,
    priority: "普通" as TaskPriority,
    status: "未开始" as TaskStatus, // 默认状态
    start: dayjs().format("YYYY-MM-DD"),
    end: dayjs().add(1, "day").format("YYYY-MM-DD"),
    parentId: parentRow?.id || -1,
    members: [],
    children: [],
    projectId: currentProjectId.value || 0,
    owner: "",
    canUpload: false,
    description: "", // 初始化描述字段
  };
  // 设置父里程碑信息
  if (parentRow) {
    const allTasks = getAllTasksRecursive(treeTableData.value);
    parentMilestone.value = findAncestorMilestone(parentRow.id!, allTasks);
    handleTypeChange(); // 触发类型变更处理
  } else {
    parentMilestone.value = null;
  }

  createDateError.value = "";
  createDialogVisible.value = true;
}

// 处理创建
async function handleCreate() {
  if (!createForm.value.title) {
    ElMessage.error("标题不能为空");
    return;
  }

  // 提交时进行日期范围校验
  if (!validateDateRange("create")) {
    return;
  }

  if (dayjs(createForm.value.start).isAfter(dayjs(createForm.value.end))) {
    ElMessage.error("开始日期不能晚于截止日期");
    return;
  }

  if (!Array.isArray(createForm.value.members)) {
    console.error("handleCreate: 人员分配数据无效");
    ElMessage.error("人员分配数据无效");
    return;
  }
  // 层级约束验证
  const hierarchyValidation = validateHierarchy(
    createForm.value.type,
    createForm.value.parentId === -1 ? null : (createForm.value.parentId ?? -1)
  );

  if (!hierarchyValidation.valid) {
    ElMessage.error(hierarchyValidation.message);
    return;
  }
  try {
    createDialogVisible.value = false;
    const isoStart = formatToIsoTime(createForm.value.start);
    const isoEnd = formatToIsoTime(createForm.value.end);

    const newTask: Omit<TaskRowWithDescription, "id"> = {
      projectId: currentProjectId.value || 0,
      title: createForm.value.title,
      type: createForm.value.type,
      priority: createForm.value.priority,
      status: createForm.value.status,
      start: isoStart,
      end: isoEnd,
      parentId:
        createForm.value.parentId === -1
          ? null
          : (createForm.value.parentId ?? null),
      members: createForm.value.members,
      owner: createForm.value.owner,
      canUpload: createForm.value.canUpload,
      finish: null,
      children: [],
      description: createForm.value.description, // 包含描述字段
    };
    await TaskAPI.create(currentProjectId.value!, newTask);
    // 强制刷新数据
    await fetchData(currentProjectId.value!);
    createDialogVisible.value = false;
    ElMessage.success("工作项创建成功");
  } catch (error) {
    console.error("handleCreate: 错误:", error);
    ElMessage.error("创建工作项失败");
  }
}

// 打开查看对话框
function openViewDialog(row: TaskRowWithDescription) {
  viewRow.value = { ...row }; // 创建副本避免引用问题
  viewDialogVisible.value = true;
}

// 打开移动对话框
function openMoveDialog(row: TaskRowWithDescription) {
  // 检查是否为里程碑
  if (row.type === "milestone") {
    ElMessage.warning("里程碑不能移动");
    return;
  }

  // 获取当前任务所属的里程碑
  const allTasks = getAllTasksRecursive(treeTableData.value);
  const currentMilestone = findAncestorMilestone(row.id!, allTasks);

  if (!currentMilestone) {
    ElMessage.error("无法确定当前任务所属的里程碑，无法移动");
    return;
  }

  moveForm.value = {
    taskId: row.id!,
    taskTitle: row.title,
    taskType: row.type,
  };
  moveDialogVisible.value = true;
}

// 处理移动
async function handleMove() {
  pageLoading.value = true;
  moveDialogVisible.value = false;
  try {
    // 获取当前要移动的任务
    const allTasks = getAllTasksRecursive(treeTableData.value);
    const currentTask = allTasks.find(
      (task) => task.id === moveForm.value.taskId
    );

    if (!currentTask) {
      ElMessage.error("找不到要移动的任务");
      return;
    }

    // 检查是否为里程碑
    if (currentTask.type === "milestone") {
      ElMessage.error("里程碑不能移动");
      return;
    }

    // 获取当前任务所属的里程碑
    const currentMilestone = findAncestorMilestone(
      moveForm.value.taskId,
      allTasks
    );

    if (!currentMilestone) {
      ElMessage.error("无法确定当前任务所属的里程碑");
      return;
    }

    // 验证目标位置
    const targetId = moveForm.value.newParentId;
    let targetMilestone: TaskRowWithDescription | null = null;

    if (targetId === -1) {
      ElMessage.error("只有里程碑可以移动到顶级");
      return;
    } else {
      // 查找目标所属的里程碑
      targetMilestone = findAncestorMilestone(targetId, allTasks);

      // 如果目标是里程碑，则目标里程碑就是它自己
      const targetTask = allTasks.find((task) => task.id === targetId);
      if (targetTask && targetTask.type === "milestone") {
        targetMilestone = targetTask;
      }
    }

    // 检查是否在同一个里程碑下
    if (!targetMilestone || targetMilestone.id !== currentMilestone.id) {
      ElMessage.error(
        `只能在当前里程碑"${currentMilestone.title}"下移动，不能跨里程碑移动`
      );
      return;
    }

    // 验证移动的层级约束
    const hierarchyValidation = validateHierarchy(
      moveForm.value.taskType,
      moveForm.value.newParentId === -1 ? null : moveForm.value.newParentId
    );

    if (!hierarchyValidation.valid) {
      ElMessage.error(hierarchyValidation.message);
      return;
    }

    await TaskAPI.switchFolder({
      newId: moveForm.value.newParentId,
      taskId: moveForm.value.taskId,
    });

    // 强制刷新数据
    await fetchData(currentProjectId.value!);
    moveDialogVisible.value = false;
    ElMessage.success("移动成功");
  } catch (error) {
    console.error("handleMove: 错误:", error);
    ElMessage.error("移动失败");
  } finally {
    pageLoading.value = false;
  }
}

// 打开删除对话框
function openDeleteDialog(row: TaskRowWithDescription) {
  deleteRow.value = { ...row }; // 创建副本避免引用问题
  deleteDialogVisible.value = true;
}

// 处理删除（包括所有子项）
async function handleDelete() {
  if (!deleteRow.value || !currentProjectId.value) return;

  try {
    deleteDialogVisible.value = false;
    // 获取所有需要删除的项ID（当前项 + 所有子项）
    const allItems = getAllTasksRecursive(treeTableData.value);
    const idsToDelete = getAllChildIds(deleteRow.value, allItems);

    if (idsToDelete.length === 0) {
      ElMessage.warning("没有可删除的项目");
      return;
    }

    // 批量删除所有相关项
    // 由于后端可能不支持批量删除，使用循环删除
    for (const id of idsToDelete) {
      await TaskAPI.delete(currentProjectId.value, id);
    }

    // 强制刷新数据
    await fetchData(currentProjectId.value);
    deleteDialogVisible.value = false;
    ElMessage.success(`已成功删除 ${idsToDelete.length} 个工作项（包括子项）`);
  } catch (error) {
    console.error("handleDelete: 错误:", error);
    ElMessage.error("删除工作项失败");
  }
}

// 打开编辑对话框
function openEditDialog(row: TaskRowWithDescription) {
  // 确保时间正确初始化
  editForm.value = {
    ...row,
    parentId: row.parentId === null ? -1 : row.parentId,
    children: row.children || [],
    members: Array.isArray(row.members) ? [...row.members] : [],
    start: row.start
      ? formatIsoToDisplay(row.start)
      : dayjs().format("YYYY-MM-DD"),
    end: row.end
      ? formatIsoToDisplay(row.end)
      : dayjs().add(1, "day").format("YYYY-MM-DD"),
    description: row.description || "", // 初始化描述字段
  };

  // 查找父里程碑
  const allTasks = getAllTasksRecursive(treeTableData.value);
  editParentMilestone.value =
    editForm.value.parentId === -1
      ? null
      : findAncestorMilestone(editForm.value.parentId, allTasks);

  editDateError.value = "";
  editDialogVisible.value = true;
}

// 处理编辑
async function handleEdit() {
  editDialogVisible.value = false;
  if (!editForm.value.title) {
    ElMessage.error("标题不能为空");
    return;
  }

  // 提交时进行日期范围校验
  if (!validateDateRange("edit")) {
    return;
  }

  if (dayjs(editForm.value.start).isAfter(dayjs(editForm.value.end))) {
    ElMessage.error("开始日期不能晚于截止日期");
    return;
  }

  if (!Array.isArray(editForm.value.members)) {
    console.error("handleEdit: 人员分配数据无效");
    ElMessage.error("人员分配数据无效");
    return;
  }
  // 确保里程碑只能是顶级任务
  if (editForm.value.type === "milestone" && editForm.value.parentId !== -1) {
    ElMessage.error("里程碑只能设置为顶级任务");
    return;
  }
  try {
    pageLoading.value = true;
    const isoStart = formatToIsoTime(editForm.value.start);
    const isoEnd = formatToIsoTime(editForm.value.end);
    const isoFinish = editForm.value.finish
      ? formatToIsoTime(editForm.value.finish)
      : null;

    await TaskAPI.update(currentProjectId.value!, editForm.value.id, {
      ...editForm.value,
      parentId: editForm.value.parentId === -1 ? null : editForm.value.parentId,
      members: editForm.value.members,
      start: isoStart,
      end: isoEnd,
      finish: editForm.value.status === "已完成" ? isoEnd : isoFinish,
      description: editForm.value.description, // 包含描述字段
    });
    // 强制刷新数据
    await fetchData(currentProjectId.value!);
    editDialogVisible.value = false;
    ElMessage.success("工作项更新成功");
  } catch (error) {
    console.error("handleEdit: 错误:", error);
    ElMessage.error("更新工作项失败");
  } finally {
    pageLoading.value = false;
  }
}

// 监听 createForm.type，确保里程碑的 parentId 为 -1
watch(
  () => createForm.value.type,
  (newType) => {
    if (newType === "milestone") {
      createForm.value.parentId = -1;
    }
  }
);

// 监听 editForm.type，确保里程碑的 parentId 为 -1
watch(
  () => editForm.value.type,
  (newType) => {
    if (newType === "milestone") {
      editForm.value.parentId = -1;
    }
  }
);

// 监听路由参数变化以刷新数据
watch(
  () => route.params.projectId,
  async (newProjectId) => {
    if (newProjectId) {
      const projectId = Number(newProjectId);
      if (projectId && projectList.value.some((p) => p.id === projectId)) {
        currentProjectId.value = projectId;
        expandedRowKeys.value = [];
        await fetchData(projectId);
      } else {
        ElMessage.error("无效的项目ID");
        router.push({ name: "DefaultRoute" });
      }
    }
  },
  { immediate: true }
);

// 生命周期钩子
onMounted(async () => {
  await initData();
});
</script>

<style scoped>
.search-wrapper {
  display: flex;
  align-items: center;
}
.input-label {
  font-size: 14px;
  color: #606266;
  user-select: none;
  white-space: nowrap;
  margin-right: 8px;
}
.search-i .milestone-title {
  font-weight: bold;
  color: #f56c6c;
}

.folder-title {
  font-weight: bold;
  color: #409eff;
}

.task-title {
  color: #67c23a;
}
</style>
