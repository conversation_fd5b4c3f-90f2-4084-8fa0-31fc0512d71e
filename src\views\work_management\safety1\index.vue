<template>
  <div :key="componentKey" class="vulnerability-management">
    <el-tabs v-model="activeTab" :class="{ 'hide-tabs-header': activeTab === 'sender' }">
      <el-tab-pane label="流程" name="template">
        <template v-if="activeTab === 'template'">
          <Process :ticketId="processParams" @init="init" />
        </template>
      </el-tab-pane>
      <el-tab-pane label="返回列表" name="sender">
        <Details @navigateToProcess="navigateToProcess" :id="getId" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>


<script lang="ts" setup>
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import Process from "./components/process.vue";
import Details from "./components/details.vue";

const activeTab = ref("sender");
const processParams = ref<number | undefined>(); 
const router = useRouter();
const route = useRoute();
const componentKey = ref(0);
// 从get请求获取id
const getId = Number(route.query.id);
const navigateToProcess = (ticketId: number | undefined) => {
  console.log("ticketId", ticketId);
  processParams.value = ticketId;
  activeTab.value = "template";
};


const init = async (id: number) => {
  console.log("id", id);
  activeTab.value = "sender";
  console.log("route.name", route.name, location.hash, route.path);
  if (route.path == "/work_management/safety") {
    router.push({
      path: '/work_management/safety/vulns', 
      query: { refresh: Date.now() } 
    });
  } else {
    router.push({
      path: '/work_management/safety', 
      query: { refresh: Date.now() } 
    });
  }

};

</script>

<style scoped>
.assessment-management {
  padding: 20px;
}

/* 隐藏标签栏 */
.hide-tabs-header :deep(.el-tabs__header) {
  display: none;
}

/* 美化标签栏 */
:deep(.el-tabs__header) {
  padding: 0 5px;
  margin-bottom: 20px;
  border: none !important;
}

:deep(.el-tabs__item) {
  font-size: 14px;
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
}

:deep(.el-tabs__item.is-active) {
  font-weight: bold;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 2px;
}

:deep(.el-tabs__nav) {
  display: flex;
  width: 100%;
}

:deep(.el-tabs__item[aria-controls="pane-sender"]) {
  margin-left: auto;
}
</style>
