<template>
  <div class="vulnerability-audit">
    <h2 class="initiate-ticket_title">漏洞审核</h2>
    <!-- 等待界面 -->
    <div v-if="!hasPermission('system:safety:audit')" class="waiting-audit">
      <el-card class="waiting-card">
        <Wait />
      </el-card>
    </div>
    <!-- 表单 -->
    <el-form v-if="hasPermission('system:safety:audit')" :model="vulnerabilityForm" label-width="140px">
      <!-- 卡片 -->
      <el-card class="mb-4">
        <template #header>
          <div class="card-header">
            <span>工单创建人信息</span>
          </div>
        </template>
          <!-- <p class="base_title">工单创建人信息</p> -->
        <el-row :gutter="24">
          <!-- <el-col :span="4">
            <el-form-item label="编号" label-width="60px" prop="id">
              <el-input v-model="vulnerabilityForm.id" disabled></el-input>
            </el-form-item>
          </el-col> -->
          <el-col :span="6">
             <el-form-item label="工号：" label-width="70px"  prop="employeeId">
              <el-input v-model="vulnerabilityForm.employeeId" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="创建人：" label-width="70px"  prop="applicant">
              <el-input disabled>
                <template v-slot:prepend>
                  <Dictmap code="user0x0" v-model="vulnerabilityForm.applicant" />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="所在单位：" label-width="90px"  prop="deptId">
              <el-input disabled>
                <template v-slot:prepend>
                  <Dictmap code="dept0x0" v-model="vulnerabilityForm.deptId" />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="联系方式：" label-width="90px" prop="mobile">
              <el-input v-model="vulnerabilityForm.mobile" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      
       <el-card class="mb-4">
        <template #header>
          <div class="card-header">
            <span>工单基本信息</span>
          </div>
        </template>
        <!-- 第二行 -->
        <!-- <p class="base_title">工单基本信息</p> -->
        <el-row :gutter="24">
          <!-- <el-col :span="4">
            <el-form-item label="应用系统名称" prop="systemId" disabled>
              <Dictionary v-model="vulnerabilityForm.systemId" disabled code="system0x0" />
            </el-form-item>
          </el-col> -->
            <!-- <el-col :span="8">
             <el-form-item label="工单任务名称：" prop="title">
          <el-input v-model="vulnerabilityForm.title" disabled></el-input>
        </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="工单创建时间：" prop="createTime" disabled>
              <el-date-picker disabled v-model="vulnerabilityForm.createTime" type="datetime" placeholder="选择日期时间"
                style="width: 100%;"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工单结束时间：" prop="deadline" disabled>
              <el-date-picker disabled v-model="vulnerabilityForm.deadline" type="datetime" placeholder="选择日期时间"
                style="width: 100%;"></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row :gutter="24">
           <el-col :span="12">
             <el-form-item label="工单任务描述：" prop="content">
          <el-input type="textarea" v-model="vulnerabilityForm.content" :rows="2" disabled></el-input>
        </el-form-item>
          </el-col>
        <el-col :span="12">
          <el-form-item label="备注：" prop="remarks" disabled>
          <el-input type="textarea" v-model="vulnerabilityForm.remarks" :rows="2" disabled></el-input>
        </el-form-item>
        </el-col>
        </el-row> -->
        </el-card>
        <el-card class="mb-4" :disabled="showStep">
        <template #header>
          <div class="card-header">
            <span>漏洞整改部门</span>
          </div>
        </template>
          <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="部门名称：">
              <el-input 
                v-model="shstep.deptName"
                disabled
              ></el-input>
        </el-form-item>
          </el-col>
          <el-col :span="8">
             <el-form-item label="联系人：" disabled>
              <el-input 
                v-model="shstep.userName"
                disabled
              ></el-input>
          
           
            </el-form-item>
          </el-col>
           <el-col :span="8">
             <el-form-item label="联系方式：">
              <el-input 
                v-model="shstep.userMobile"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          </el-row>
        </el-card>
        <!-- <el-card class="mb-4">
        <template #header>
          <div class="card-header">
            <span>资产基本信息</span>
          </div>
        </template> -->
          <!-- <p class="base_title">工单创建人信息</p> -->
          <!-- 资产基本信息开始 -->
        <!-- <div class="asset-select-box">
          <div>
            <p class="base_title">资产基本信息</p>
          </div>
        </div> -->
        <!-- 资产表格 -->
        <!-- <el-table
          :data="vulnerabilityForm.assetsList"
          style="width: 100%"
          border
          stripe
          size="small"
          class="assets-table"
        > -->
          <!-- 序号列 -->
          <!-- <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          /> -->

          <!-- 资产类型 -->
          <!-- <el-table-column
            prop="type"
            label="资产类型"
            show-overflow-tooltip
            align="center"
            width="120"
          >
            <template #default="scope">
              <el-tag size="small" class="asset-type-tag">
                {{ getAssetTypeName(Number(scope.row.type)) }}
              </el-tag>
            </template>
          </el-table-column> -->
          <!-- 资产名称 -->
          <!-- <el-table-column
            prop="name"
            align="center"
            label="资产名称"
            show-overflow-tooltip
          /> -->

          <!-- 资产状态 -->
          <!-- <el-table-column prop="status" label="状态" width="80" align="center">
            <template #default="scope">
              <el-tag
                size="small"
                :type="
                  scope.row.status === '1'
                    ? 'success'
                    : scope.row.status === '0'
                    ? 'danger'
                    : 'info'
                "
              >
                {{
                  scope.row.status === "1"
                    ? "正常"
                    : scope.row.status === "0"
                    ? "异常"
                    : "废弃"
                }}
              </el-tag>
            </template>
          </el-table-column> -->

          <!-- 资产地址 -->
          <!-- <el-table-column
            prop="address"
            label="资产地址"
            align="center"
            show-overflow-tooltip
            min-width="150"
          >
            <template #default="scope">
              <span>{{ scope.row.ip || scope.row.url || "-" }}</span>
            </template>
          </el-table-column> -->
          <!-- 管理部门 -->
          <!-- <el-table-column
            prop="deptId"
            label="管理部门"
            show-overflow-tooltip
            align="center"
            width="180"
          >
            <template #default="scope">
              <Dictmap v-model="scope.row.deptId" code="dept0x0" />
            </template>
          </el-table-column> -->

          <!-- 管理员 -->
          <!-- <el-table-column
            prop="managerName"
            label="管理员"
            width="100"
            align="center"
            show-overflow-tooltip
          />
        </el-table> -->
        <!-- 资产基本信息结束 -->
        
      <!-- </el-card> -->
      <!-- 安全工程师信息 -->
      <el-card class="mb-4" :disabled="showStep">
        <template #header>
          <div class="card-header">
          <span>
             安全工程师信息
             <el-tooltip
            placement="top"
            effect="light"
            content="工程师基本信息"
            :raw-content="true"
          >
            <el-icon style="vertical-align: -0.15em" size="16">
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
          </span>
          </div>
        </template>
        <!-- <p class="base_title">
          安全工程师信息
        </p> -->
        <!-- 安全工程师信息开始 -->
        <el-form  :model="vulnerabilityForm" :inline="true" >
        <div>
              <el-form-item label-width="100px" label="工程师名称：" prop="engineerName" style="width:20%">
              <el-input disabled v-model="vulnerabilityForm.engineerName"></el-input>
            </el-form-item>
            <el-form-item label="联系方式：" label-width="90px" prop="engineerMobile" style="width:20%">
              <el-input  disabled v-model="vulnerabilityForm.engineerMobile"> </el-input>
            </el-form-item>
            <el-form-item label="微信号：" label-width="80px" prop="engineerWechat" style="width:20%">
              <el-input disabled v-model="vulnerabilityForm.engineerWechat"></el-input>
            </el-form-item>
            <el-form-item label="QQ号：" label-width="70px" prop="engineerQq" style="width:20%">
              <el-input disabled v-model="vulnerabilityForm.engineerQq"></el-input>
            </el-form-item>
            <el-form-item label="邮箱：" label-width="60px" prop="engineerEmail" style="width:20%">
              <el-input disabled v-model="vulnerabilityForm.engineerEmail"></el-input>
            </el-form-item>
        </div>
        </el-form>
        <!-- 安全工程师信息结束 -->
      </el-card>
       <!-- <el-card class="mb-4">
        <template #header>
          <div class="card-header">
            <span>文档下载</span>
          </div>
        </template>
        <el-table :data="fileList" border>
          <el-table-column align="center"  prop="linkName" label="环节名称"></el-table-column>
          <el-table-column align="center"  prop="name" label="文件名"></el-table-column>
          <el-table-column align="center"  prop="createByName" label="创建人名称"></el-table-column>
          <el-table-column align="center"  prop="createTime" label="创建时间"></el-table-column> -->
          <!-- 操作按钮 -->
          <!-- <el-table-column
            label="操作"
            width="140"
            align="center"
          >
            <template #default="scope">
              <el-button type="primary" style="margin-left:20px" @click="downloadFile(scope.row.file)">下载</el-button>
            </template>
          </el-table-column>
        </el-table> -->
         <!--  -->
       <!-- </el-card> -->
      <!-- 漏洞风险信息 -->
      <el-card class="mb-4">
        <template #header>
          <div class="card-header">
            <span>漏洞风险信息</span>
          </div>
        </template>
        <el-row :gutter="24">
           <el-col :span="12">
             <el-form-item  label-width="140px" label="信息系统名称：" prop="title">
          <el-input v-model="vulnerabilityForm.title" disabled maxlength="10"></el-input>
        </el-form-item>
          </el-col>
           <el-col :span="12">
            <el-form-item  label-width="140px" label="域名/IP：" prop="domainIp" disabled>
             <el-input disabled v-model="vulnerabilityForm.domainIp" ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="fontw" label-width="140px" label="漏洞来源：" prop="loopholeSource" disabled>
              <Dictionary v-model="vulnerabilityForm.loopholeSource" code="VLUNS_O"
                disabled></Dictionary>
            </el-form-item>
          </el-col>
           <el-col :span="12">
             <el-form-item  class="fontw" label-width="140px" label="安全漏洞报告：">
                <ul  class="fileList_ul">
                  <li v-for="file in fileList" :key="file.id">
                    <span>{{ file.name }}</span>
                    <el-button type="primary" style="margin-left:20px" @click="downloadFile(file)">下载</el-button>
                  </li>
                  <li v-if="fileList.length<=0">无</li>
                </ul>
              </el-form-item>
           </el-col>
        
          <el-col :span="24">
             <el-form-item  label-width="140px" label="安全漏洞情况描述：" prop="content">
          <el-input type="textarea" v-model="vulnerabilityForm.content" :rows="2" disabled></el-input>
        </el-form-item>
          </el-col>
        </el-row>
         <el-form-item class="fontw" label-width="140px" label="安全漏洞详情：" prop="" disabled>
           <el-table :data="vulnerabilityList" style="width: 100%;margin-bottom:20px" disabled border>
          <!-- <el-table-column align="center" width="150" prop="id" label="序号"></el-table-column> -->
          <el-table-column align="center"  prop="name" label="漏洞名称"></el-table-column>
          <el-table-column align="center"  prop="level" label="漏洞级别"></el-table-column>
          <el-table-column align="center" prop="ip" label="服务器IP"></el-table-column>
          <el-table-column align="center"  prop="url" label="URL地址"></el-table-column>
          <el-table-column align="center" prop="remark" label="漏洞描述"
            show-overflow-tooltip></el-table-column>
             <el-table-column
            align="center"
             show-overflow-tooltip
            prop="repairSuggestions"
            label="修复建议"
          ></el-table-column>
          <el-table-column align="center"  prop="fix" label="是否修复">
            <template #default="scope">
              <el-tag :type="scope.row.fix === 0 ? 'danger' : 'success'">
                {{ scope.row.fix === 1 ? '已修复' : '未修复' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="createTime" label="漏洞创建时间"
            show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="updateTime" label="漏洞更新时间"
            show-overflow-tooltip></el-table-column>
        </el-table>
        </el-form-item>
       
       
      </el-card>
      <!-- 审核内容 -->
      <el-card class="mb-4">
        <template #header>
          <div class="card-header">
            <span>审核内容</span>
          </div>
        </template>
        <!-- 审核意见 -->
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="审核意见：">
              <el-input type="textarea" v-model="auditForm.commentContent" :rows="4" :disabled="!showStep"></el-input>
            </el-form-item>
          </el-col>
         
        </el-row>
         <el-row :gutter="24">
           <el-col :span="24">
             <!-- 审核结果 -->
        <el-form-item label="审核结果：">
          <Dictionary v-model="auditForm.commentType" code="VLUNS_E" :disabled="!showStep" />
        </el-form-item>
          </el-col>
         </el-row>
        <el-row :gutter="24">
           <el-col :span="24">
            <!-- 时间 -->
        <el-form-item label="审核时间：">
          <el-date-picker v-model="auditForm.createTime" type="datetime" placeholder="选择日期时间"
            format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" disabled></el-date-picker>
        </el-form-item>
           </el-col>
        </el-row>
        
        <!-- 添加文件上传功能 -->
        <el-form-item label="" v-if="showStep">
          <file-upload uploadBtnText="上传审核附件" :upload-max-size="20 * 1024 * 1024" v-model="auditFileList"
            :accept="'.pdf,.xls,.doc,.docx,.txt,.csv,.xlsx'" :tip="'仅支持pdf，excel,word格式的文件，且大小不超过20MB'">
          </file-upload>
          <div class="upload-tip">仅支持pdf，excel,word格式的文件，且大小不超过20MB</div>
        </el-form-item>

        <!-- 已上传文件列表 -->
        <el-form-item label="审核附件" v-if="!showStep && auditFileList.length > 0">
          <div class="file-list">
            <el-tag v-for="file in auditFileList" :key="file.id" class="file-item" @click="downloadFile(file)">
              <el-icon>
                <document />
              </el-icon>
              {{ file.name }}
            </el-tag>
          </div>
        </el-form-item>
        <!-- 表单操作按钮 -->
        <div class="form-actions" v-if="showStep">
          <el-button type="primary" @click="submitAudit">提交审核</el-button>
          <el-button type="info" @click="concelForm" >取消</el-button>
        </div>
      </el-card>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import  { StepStatus } from '@/api/work_management/safety/pocess'
import safetyAPI, { safetyPageVO, safetyForm, safetyPageQuery } from "@/api/work_management/safety";
import { ElMessage } from 'element-plus';
import { hasAuth } from "@/plugins/permission";
import Wait from "@/views/error-page/401.vue";
import {formatLocalDateTime} from "@/utils/dateUtils";
import { useRouter, useRoute } from "vue-router";
import { useDictStore } from '@/store/modules/dictStore'
const dictStore = useDictStore()
const router = useRouter();
interface TicketData {
  id: number;
  currentStep: string;
}

const props = defineProps<{
  ticketdata: TicketData,
}>();
const emit = defineEmits(['next'])
const ID = ref(props.ticketdata ? props.ticketdata.id : null);
const currentStep = ref(props.ticketdata.currentStep);
const nowStep = ref('');
const showStep = ref(false);
const stepStatus = ref<StepStatus>({});
// 表单数据
const vulnerabilityForm = reactive<safetyForm>({});
const auditForm = ref<any>({
  commentContent: '',
  commentType: undefined,
  createTime: formatLocalDateTime(new Date()),
  updateTime: formatLocalDateTime(new Date())
});
// 资产类型
// 获取资产类型名称
const getAssetTypeName = (type: number) => {
  const typeMap: Record<number, string> = {
    "-1": "未知类型",
    1: "服务器",
    2: "网络设备",
    3: "安全设备",
    10: "信息系统",
  };
  return typeMap[type] || "未知类型";
};
// 整改部门信息
const shstep=ref({})
// 文件列表
const fileList = ref<any[]>([]);

const auditFileList = ref<any[]>([]);

// 漏洞列表数据
const vulnerabilityList = ref<Array<any>>([]);

function isCurrentStep() {
  if (currentStep.value == nowStep.value) { //是当前步骤
    showStep.value = true;
  } else {
    showStep.value = false;
  }
}

async function handleQuery() {
  await safetyAPI.getFormData(props.ticketdata.id).then((res) => {
    console.log(res)
    Object.assign(vulnerabilityForm, res);
  });
  console.log(vulnerabilityForm)
  // 整改部门信息
   const foundItem = vulnerabilityForm.reviewProcessForms?.find(item => item.executeDeptType == 3);
   shstep.value=foundItem
  vulnerabilityList.value = vulnerabilityForm.vulns || [];
  fileList.value=vulnerabilityForm.fileList
  const statusRes: any = await safetyAPI.getStepStatus(props.ticketdata.id);
  stepStatus.value = statusRes;
  for (const step in stepStatus.value) {
    if (stepStatus.value[step as keyof StepStatus] == 'process') {
      nowStep.value = step as keyof StepStatus;
      break;
    }
  }
  // safetyAPI.getFileList(ID.value).then((data) => {
  //   fileList.value = data;
  // })
  isCurrentStep();
  if (!showStep.value) {

    let filteredComments = vulnerabilityForm.comments ? vulnerabilityForm.comments.filter((item: any) => item.step == 2) : [];
    auditForm.value = filteredComments[filteredComments.length - 1];
  }
}

// 附件下载
function downloadFile(file: { name: string; url: string }) {
   if (file.url) {
  fetch(file.url)
  .then(response => response.blob())
  .then(blob => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = file.name; // 设置自定义文件名
    link.style.display = 'none'; // 隐藏链接
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  })
  .catch(error => {
      ElMessage.error('附件不存在');
  }); 
}
}


// 提交审核
const submitAudit = async () => {
  
  await ElMessageBox.confirm(
    '确定要提交漏洞审核吗？',
    '确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  );
  try {
    auditForm.value.fileList = auditFileList.value.map(file => file.id);

    await safetyAPI.auditTicket(props.ticketdata.id, auditForm.value);
    // 显示成功消息
    ElMessage.success('审核已提交');
    console.log('emit', props.ticketdata.id);
    emit('next', props.ticketdata.id);
  } catch (error) {
    console.error('Error submitting audit:', error);
    ElMessage.error('提交失败，请重试');
  }
}
// 取消
const concelForm=()=>{
 router.push({
      path: '/work_management/safety', 
      query: { refresh: Date.now() } 
    });
}
// 权限判断
function hasPermission(requiredPerms: string): boolean {
  console.log(hasAuth(requiredPerms, 'button'));
  return hasAuth(requiredPerms, 'button');
}

onMounted(() => {
  handleQuery();
})
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-actions {
  margin-top: 20px;
  text-align: center;
}

.initiate-ticket_title {
  margin-top: 0;
}
.base_title {
  font-weight: bold;
  margin: 0;
  padding: 0;
  height: 44px;
  line-height: 44px;
  font-size: 16px !important;
}
:deep(.el-form-item) {
  margin-right: 0;
}
.fontw  :deep(.el-form-item__label) {
  /* color: #333; */
  /* font-weight: bold; */
}
.fileList_ul{
  width: 100%;
}
 .fileList_ul li{
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f7f6f6;
  padding: 5px;
  border-radius: 4px;
  margin-bottom: 10px;
  }
</style>
